/**
 * Migration script to move files from Firebase Storage to Google Drive
 * 
 * Usage:
 * node scripts/migrate-to-google-drive.js
 */

// Load environment variables
require('dotenv').config();

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, doc, updateDoc } = require('firebase/firestore');
const { getStorage, ref, getDownloadURL, getBlob } = require('firebase/storage');
const { google } = require('googleapis');
const { Readable } = require('stream');
const fetch = require('node-fetch');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const storage = getStorage(app);

// Define folder paths
const FOLDERS = {
  PROFILE_PHOTOS: 'profile_photos',
  MATERIALS: 'materials',
  SUBMISSIONS: 'submissions',
};

// Initialize the Google Drive API client
const initializeDriveClient = () => {
  try {
    // Service account authentication
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.VITE_GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.VITE_GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/drive'],
    });

    // Create the drive client
    return google.drive({ version: 'v3', auth });
  } catch (error) {
    console.error('Error initializing Google Drive client:', error);
    throw new Error('Failed to initialize Google Drive client');
  }
};

// Get or create a folder in Google Drive
const getOrCreateFolder = async (drive, folderName, parentFolderId) => {
  try {
    // Check if folder already exists
    const query = parentFolderId 
      ? `name='${folderName}' and mimeType='application/vnd.google-apps.folder' and '${parentFolderId}' in parents and trashed=false`
      : `name='${folderName}' and mimeType='application/vnd.google-apps.folder' and trashed=false`;
    
    const response = await drive.files.list({
      q: query,
      fields: 'files(id, name)',
      spaces: 'drive',
    });

    // If folder exists, return its ID
    if (response.data.files && response.data.files.length > 0) {
      return response.data.files[0].id;
    }

    // If folder doesn't exist, create it
    const fileMetadata = {
      name: folderName,
      mimeType: 'application/vnd.google-apps.folder',
      parents: parentFolderId ? [parentFolderId] : undefined,
    };

    const folder = await drive.files.create({
      requestBody: fileMetadata,
      fields: 'id',
    });

    if (!folder.data.id) {
      throw new Error('Failed to create folder');
    }

    // Make the folder publicly accessible for viewing
    await drive.permissions.create({
      fileId: folder.data.id,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });

    return folder.data.id;
  } catch (error) {
    console.error(`Error getting or creating folder ${folderName}:`, error);
    throw new Error(`Failed to get or create folder: ${folderName}`);
  }
};

// Upload a file to Google Drive
const uploadFileToDrive = async (drive, fileBuffer, fileName, folderPath, mimeType) => {
  try {
    // Split folder path and ensure all folders exist
    const folderNames = folderPath.split('/').filter(name => name.trim() !== '');
    let parentId = undefined;
    
    // Create folder hierarchy if needed
    for (const folderName of folderNames) {
      parentId = await getOrCreateFolder(drive, folderName, parentId);
    }
    
    // Create a readable stream from the buffer
    const fileStream = new Readable();
    fileStream.push(fileBuffer);
    fileStream.push(null);
    
    // Upload the file
    const fileMetadata = {
      name: fileName,
      parents: parentId ? [parentId] : undefined,
    };
    
    const media = {
      mimeType: mimeType || 'application/octet-stream',
      body: fileStream,
    };
    
    const uploadedFile = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, name, mimeType, webViewLink, webContentLink, size, createdTime',
    });
    
    if (!uploadedFile.data.id) {
      throw new Error('Failed to upload file');
    }
    
    // Make the file publicly accessible for viewing
    await drive.permissions.create({
      fileId: uploadedFile.data.id,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });
    
    return uploadedFile.data;
  } catch (error) {
    console.error(`Error uploading file ${fileName}:`, error);
    throw new Error(`Failed to upload file: ${fileName}`);
  }
};

// Download file from URL
const downloadFile = async (url) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }
    return await response.buffer();
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
};

// Migrate materials
const migrateMaterials = async () => {
  try {
    console.log('Migrating materials...');
    const drive = initializeDriveClient();
    
    const materialsCollection = collection(db, 'materials');
    const materialsSnapshot = await getDocs(materialsCollection);
    
    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    for (const materialDoc of materialsSnapshot.docs) {
      const material = materialDoc.data();
      
      // Skip if no file or already migrated
      if (!material.file_url || material.file_id) {
        skippedCount++;
        continue;
      }
      
      try {
        console.log(`Migrating material: ${material.title}`);
        
        // Download file from Firebase Storage
        const fileBuffer = await downloadFile(material.file_url);
        
        // Upload to Google Drive
        const uploadedFile = await uploadFileToDrive(
          drive,
          fileBuffer,
          material.file_name,
          FOLDERS.MATERIALS,
          null // We don't have mime type info in the database
        );
        
        // Update document with Google Drive info
        await updateDoc(doc(db, 'materials', materialDoc.id), {
          file_id: uploadedFile.id,
          file_url: uploadedFile.webViewLink || uploadedFile.webContentLink,
        });
        
        migratedCount++;
        console.log(`Successfully migrated material: ${material.title}`);
      } catch (error) {
        console.error(`Error migrating material ${material.title}:`, error);
        errorCount++;
      }
    }
    
    console.log(`Materials migration complete. Migrated: ${migratedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}`);
  } catch (error) {
    console.error('Error migrating materials:', error);
  }
};

// Migrate submissions
const migrateSubmissions = async () => {
  try {
    console.log('Migrating submissions...');
    const drive = initializeDriveClient();
    
    const submissionsCollection = collection(db, 'submissions');
    const submissionsSnapshot = await getDocs(submissionsCollection);
    
    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    for (const submissionDoc of submissionsSnapshot.docs) {
      const submission = submissionDoc.data();
      
      // Skip if no file or already migrated
      if (!submission.file_url || submission.file_id) {
        skippedCount++;
        continue;
      }
      
      try {
        console.log(`Migrating submission for student: ${submission.student_id}, assignment: ${submission.assignment_id}`);
        
        // Download file from Firebase Storage
        const fileBuffer = await downloadFile(submission.file_url);
        
        // Upload to Google Drive
        const folderPath = `${FOLDERS.SUBMISSIONS}/${submission.student_id}/${submission.assignment_id}`;
        const uploadedFile = await uploadFileToDrive(
          drive,
          fileBuffer,
          submission.file_name,
          folderPath,
          null // We don't have mime type info in the database
        );
        
        // Update document with Google Drive info
        await updateDoc(doc(db, 'submissions', submissionDoc.id), {
          file_id: uploadedFile.id,
          file_url: uploadedFile.webViewLink || uploadedFile.webContentLink,
        });
        
        migratedCount++;
        console.log(`Successfully migrated submission for student: ${submission.student_id}`);
      } catch (error) {
        console.error(`Error migrating submission for student ${submission.student_id}:`, error);
        errorCount++;
      }
    }
    
    console.log(`Submissions migration complete. Migrated: ${migratedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}`);
  } catch (error) {
    console.error('Error migrating submissions:', error);
  }
};

// Migrate student profile photos
const migrateProfilePhotos = async () => {
  try {
    console.log('Migrating profile photos...');
    const drive = initializeDriveClient();
    
    const studentsCollection = collection(db, 'students');
    const studentsSnapshot = await getDocs(studentsCollection);
    
    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    for (const studentDoc of studentsSnapshot.docs) {
      const student = studentDoc.data();
      
      // Skip if no passport picture or already contains Google Drive URL
      if (!student.passport_picture || student.passport_picture.includes('drive.google.com')) {
        skippedCount++;
        continue;
      }
      
      try {
        console.log(`Migrating profile photo for student: ${student.name}`);
        
        // Extract file name from URL
        const fileName = student.passport_picture.split('/').pop() || `${student.id}_profile.jpg`;
        
        // Download file
        const fileBuffer = await downloadFile(student.passport_picture);
        
        // Upload to Google Drive
        const uploadedFile = await uploadFileToDrive(
          drive,
          fileBuffer,
          fileName,
          FOLDERS.PROFILE_PHOTOS,
          'image/jpeg' // Assume JPEG for profile photos
        );
        
        // Update document with Google Drive info
        await updateDoc(doc(db, 'students', studentDoc.id), {
          passport_picture: uploadedFile.webViewLink || uploadedFile.webContentLink,
          profile_photo_id: uploadedFile.id, // Store the file ID
        });
        
        migratedCount++;
        console.log(`Successfully migrated profile photo for student: ${student.name}`);
      } catch (error) {
        console.error(`Error migrating profile photo for student ${student.name}:`, error);
        errorCount++;
      }
    }
    
    console.log(`Profile photos migration complete. Migrated: ${migratedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}`);
  } catch (error) {
    console.error('Error migrating profile photos:', error);
  }
};

// Main migration function
const migrateAllFiles = async () => {
  try {
    console.log('Starting migration from Firebase Storage to Google Drive...');
    
    // Migrate materials
    await migrateMaterials();
    
    // Migrate submissions
    await migrateSubmissions();
    
    // Migrate profile photos
    await migrateProfilePhotos();
    
    console.log('Migration complete!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

// Run the migration
migrateAllFiles().catch(console.error); 