"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
// Script to add new courses and their associated levels to the database
import * as dotenv from 'dotenv';
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, query, where, getDocs, serverTimestamp } from 'firebase/firestore';
// Load environment variables
dotenv.config();
// Firebase configuration
const firebaseConfig = {
    apiKey: process.env.VITE_FIREBASE_API_KEY,
    authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.VITE_FIREBASE_APP_ID,
    measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};
console.log('Using Firebase config:', {
    projectId: firebaseConfig.projectId,
    authDomain: firebaseConfig.authDomain
});
// Check if Firebase config is valid
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
    console.error('Firebase configuration is incomplete. Please check your .env file.');
    process.exit(1);
}
// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
// Courses to add
const coursesToAdd = [
    {
        name: "International Female Tailoring, Computer, and Language Classes",
        code: "IFTCLC",
        description: "Comprehensive training for women in tailoring, computer skills, and language learning",
        duration: "12 months",
        fee: 500,
        status: "active",
        levels: [
            { name: "JSS 1 Female", code: "IFTCLC-JSS1F", description: "Beginner tailoring and computer skills for females", order: 1 },
            { name: "JSS 2 Female", code: "IFTCLC-JSS2F", description: "Intermediate tailoring and language skills for females", order: 2 },
            { name: "JSS 3 Female", code: "IFTCLC-JSS3F", description: "Advanced tailoring, computer and language skills for females", order: 3 }
        ]
    },
    {
        name: "International Youth Academy",
        code: "IYA",
        description: "Educational program for youth focusing on academic and personal development",
        duration: "24 months",
        fee: 600,
        status: "active",
        levels: [
            { name: "JSS 1 Boys", code: "IYA-JSS1B", description: "Foundation level for junior secondary school boys", order: 1 },
            { name: "JSS 2 Boys", code: "IYA-JSS2B", description: "Intermediate level for junior secondary school boys", order: 2 },
            { name: "JSS 3 Boys", code: "IYA-JSS3B", description: "Advanced level for junior secondary school boys", order: 3 },
            { name: "JSS 1 Girls", code: "IYA-JSS1G", description: "Foundation level for junior secondary school girls", order: 4 }
        ]
    },
    {
        name: "Higher Islamic Sciences and Technology Course",
        code: "HISTC",
        description: "Specialized course combining Islamic studies with modern technology",
        duration: "36 months",
        fee: 700,
        status: "active",
        levels: [
            { name: "SSS Combined", code: "HISTC-SSSC", description: "Senior secondary school comprehensive program", order: 1 }
        ]
    },
    {
        name: "Weekend Classes",
        code: "WC",
        description: "Flexible weekend educational programs for students with weekday commitments",
        duration: "9 months",
        fee: 450,
        status: "active",
        levels: [
            { name: "Primary 1", code: "WC-P1", description: "Primary 1 weekend program", order: 1 },
            { name: "Primary 2", code: "WC-P2", description: "Primary 2 weekend program", order: 2 },
            { name: "Primary 3", code: "WC-P3", description: "Primary 3 weekend program", order: 3 },
            { name: "Primary 4", code: "WC-P4", description: "Primary 4 weekend program", order: 4 },
            { name: "Primary 5", code: "WC-P5", description: "Primary 5 weekend program", order: 5 },
            { name: "Primary 6", code: "WC-P6", description: "Primary 6 weekend program", order: 6 }
        ]
    },
    {
        name: "Masjid Education Classes",
        code: "MEC",
        description: "Educational classes focused on Islamic studies and Quranic education",
        duration: "12 months",
        fee: 300,
        status: "active",
        levels: [
            { name: "Tahfeez Beginner", code: "MEC-TB", description: "Foundation Islamic studies and basic Quran memorization", order: 1 },
            { name: "Tahfeez Intermediate", code: "MEC-TI", description: "Intermediate Islamic studies and continued Quran memorization", order: 2 },
            { name: "Tahfeez Advanced", code: "MEC-TA", description: "Advanced Islamic studies and comprehensive Quran memorization", order: 3 },
            { name: "Arabic Language", code: "MEC-AL", description: "Specialized Arabic language studies", order: 4 }
        ]
    }
];
// Helper function to check if a course already exists
async function courseExists(code) {
    const coursesRef = collection(db, "courses");
    const q = query(coursesRef, where("code", "==", code));
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
}
// Add a course to Firestore
async function addCourse(course) {
    try {
        // Check if course already exists
        if (await courseExists(course.code)) {
            console.log(`Course ${course.name} (${course.code}) already exists. Skipping.`);
            return null;
        }
        // Add created_at and updated_at timestamps
        const courseData = {
            ...course,
            created_at: serverTimestamp(),
            updated_at: serverTimestamp()
        };
        // Remove levels array before saving course
        const { levels, ...courseToSave } = courseData;
        // Add the course
        const courseRef = await addDoc(collection(db, "courses"), courseToSave);
        console.log(`Added course ${course.name} with ID: ${courseRef.id}`);
        return {
            id: courseRef.id,
            levels: levels
        };
    }
    catch (error) {
        console.error(`Error adding course ${course.name}:`, error);
        return null;
    }
}
// Add a level to Firestore
async function addLevel(level, courseId) {
    try {
        // Add course_id, created_at, and updated_at
        const levelData = {
            ...level,
            course_id: courseId,
            status: "active",
            created_at: serverTimestamp(),
            updated_at: serverTimestamp()
        };
        // Add the level
        const levelRef = await addDoc(collection(db, "levels"), levelData);
        console.log(`Added level ${level.name} with ID: ${levelRef.id} for course ID: ${courseId}`);
        return levelRef.id;
    }
    catch (error) {
        console.error(`Error adding level ${level.name}:`, error);
        return null;
    }
}
// Main function to add all courses and their levels
async function addCoursesAndLevels() {
    console.log("Starting to add courses and levels...");
    for (const course of coursesToAdd) {
        // Add the course
        const result = await addCourse(course);
        if (result) {
            // Add all levels for this course
            for (const level of result.levels) {
                await addLevel(level, result.id);
            }
        }
    }
    console.log("Finished adding courses and levels.");
}
// Execute the main function
addCoursesAndLevels()
    .then(() => {
    console.log("Script completed successfully");
    process.exit(0);
})
    .catch(error => {
    console.error("Script failed:", error);
    process.exit(1);
});
