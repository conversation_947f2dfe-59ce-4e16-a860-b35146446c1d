// <PERSON><PERSON><PERSON> to check if there are any courses in the database
import * as dotenv from 'dotenv';
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, where } from 'firebase/firestore';

// Try to load environment variables from different possible locations
try {
  // First try .env in the root directory
  dotenv.config({ path: '../.env' });
  
  // Then try .env in the current directory
  dotenv.config({ path: './.env' });
  
  // Also try .env.local if available
  dotenv.config({ path: '../.env.local' });
  dotenv.config({ path: './.env.local' });
} catch (error) {
  console.warn('Warning: Error loading environment variables:', error.message);
}

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

console.log('Using Firebase config:', {
  projectId: firebaseConfig.projectId,
  apiKey: firebaseConfig.apiKey ? '******' : undefined
});

// Check if Firebase config is valid
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error('Firebase configuration is incomplete. Please check your .env file.');
  process.exit(1);
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Function to check if there are any courses
async function checkCourses() {
  console.log('\n========== COURSES ==========');
  console.log('Checking for courses in Firestore...');
  
  try {
    const coursesRef = collection(db, 'courses');
    const snapshot = await getDocs(coursesRef);
    
    if (snapshot.empty) {
      console.log('No courses found in the database.');
    } else {
      console.log(`Found ${snapshot.size} courses:`);
      snapshot.forEach(doc => {
        const data = doc.data();
        console.log(`\n📚 [${data.code}] ${data.name}`);
        console.log(`   ID: ${doc.id}`);
        console.log(`   Duration: ${data.duration}`);
        console.log(`   Fee: ${data.fee}`);
        console.log(`   Status: ${data.status}`);
      });
    }
    
  } catch (error) {
    console.error('Error checking courses:', error);
  }
}

// Function to check if there are any levels
async function checkLevels() {
  console.log('\n========== LEVELS ==========');
  console.log('Checking for levels in Firestore...');
  
  try {
    const levelsRef = collection(db, 'levels');
    const snapshot = await getDocs(levelsRef);
    
    if (snapshot.empty) {
      console.log('No levels found in the database.');
    } else {
      // Group levels by course_id
      const levelsByCourseid = {};
      
      for (const doc of snapshot.docs) {
        const data = doc.data();
        const courseId = data.course_id;
        
        if (!levelsByCourseid[courseId]) {
          levelsByCourseid[courseId] = [];
        }
        
        levelsByCourseid[courseId].push({
          id: doc.id,
          ...data
        });
      }
      
      console.log(`Found ${snapshot.size} levels across ${Object.keys(levelsByCourseid).length} courses:`);
      
      // Get course names for better display
      for (const courseId in levelsByCourseid) {
        // Get course details
        try {
          const courseRef = collection(db, 'courses');
          const q = query(courseRef, where('__name__', '==', courseId));
          const courseSnapshot = await getDocs(q);
          
          let courseName = 'Unknown Course';
          let courseCode = 'N/A';
          
          if (!courseSnapshot.empty) {
            const courseData = courseSnapshot.docs[0].data();
            courseName = courseData.name;
            courseCode = courseData.code;
          }
          
          // Display course info and its levels
          console.log(`\n🏫 Course: ${courseName} [${courseCode}]`);
          console.log(`   Course ID: ${courseId}`);
          console.log('   Levels:');
          
          // Sort levels by order if available
          const levels = levelsByCourseid[courseId].sort((a, b) => (a.order || 0) - (b.order || 0));
          
          levels.forEach(level => {
            console.log(`     📝 [${level.code}] ${level.name}`);
            console.log(`        Description: ${level.description}`);
            console.log(`        Order: ${level.order || 'Not specified'}`);
            console.log(`        Status: ${level.status || 'Not specified'}`);
            console.log(`        ID: ${level.id}`);
          });
          
        } catch (error) {
          console.error(`Error fetching course details for ${courseId}:`, error);
          
          // Still show levels even if we couldn't get course details
          console.log(`\n🏫 Course ID: ${courseId} (Unable to fetch details)`);
          console.log('   Levels:');
          
          levelsByCourseid[courseId].forEach(level => {
            console.log(`     📝 [${level.code}] ${level.name}`);
          });
        }
      }
    }
    
  } catch (error) {
    console.error('Error checking levels:', error);
  }
}

// Run both functions
console.log('====================================');
console.log('CHECKING COURSES AND LEVELS IN FIREBASE');
console.log('====================================');

checkCourses()
  .then(() => checkLevels())
  .then(() => {
    console.log('\n====================================');
    console.log('Check completed successfully.');
    console.log('====================================');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  }); 