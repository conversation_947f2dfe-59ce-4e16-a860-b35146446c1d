// Script to add new courses and their associated levels to the Firestore database
import * as dotenv from 'dotenv';
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, query, where, getDocs, serverTimestamp } from 'firebase/firestore';

// Load environment variables
dotenv.config();

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

console.log('Using Firebase config:', {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain
});

// Check if Firebase config is valid
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error('Firebase configuration is incomplete. Please check your .env file.');
  process.exit(1);
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Courses to add
const coursesToAdd = [
  {
    name: "International Female Tailoring, Computer, and Language Classes",
    code: "IFTCLC",
    description: "Comprehensive training for women in tailoring, computer skills, and language learning",
    duration: "12 months",
    fee: 500,
    status: "active",
    levels: [
      { name: "JSS 1 Female", code: "IFTCLC-JSS1F", description: "Beginner tailoring and computer skills for females", order: 1 },
      { name: "JSS 2 Female", code: "IFTCLC-JSS2F", description: "Intermediate tailoring and language skills for females", order: 2 },
      { name: "JSS 3 Female", code: "IFTCLC-JSS3F", description: "Advanced tailoring, computer and language skills for females", order: 3 }
    ]
  },
  {
    name: "International Youth Academy",
    code: "IYA",
    description: "Educational program for youth focusing on academic and personal development",
    duration: "24 months",
    fee: 600,
    status: "active",
    levels: [
      { name: "JSS 1 Boys", code: "IYA-JSS1B", description: "Foundation level for junior secondary school boys", order: 1 },
      { name: "JSS 2 Boys", code: "IYA-JSS2B", description: "Intermediate level for junior secondary school boys", order: 2 },
      { name: "JSS 3 Boys", code: "IYA-JSS3B", description: "Advanced level for junior secondary school boys", order: 3 },
      { name: "JSS 1 Girls", code: "IYA-JSS1G", description: "Foundation level for junior secondary school girls", order: 4 }
    ]
  },
  {
    name: "Higher Islamic Sciences and Technology Course",
    code: "HISTC",
    description: "Specialized course combining Islamic studies with modern technology",
    duration: "36 months",
    fee: 700,
    status: "active",
    levels: [
      { name: "SSS Combined", code: "HISTC-SSSC", description: "Senior secondary school comprehensive program", order: 1 }
    ]
  },
  {
    name: "Weekend Classes",
    code: "WC",
    description: "Flexible weekend educational programs for students with weekday commitments",
    duration: "9 months",
    fee: 450,
    status: "active",
    levels: [
      { name: "Primary 1", code: "WC-P1", description: "Primary 1 weekend program", order: 1 },
      { name: "Primary 2", code: "WC-P2", description: "Primary 2 weekend program", order: 2 },
      { name: "Primary 3", code: "WC-P3", description: "Primary 3 weekend program", order: 3 },
      { name: "Primary 4", code: "WC-P4", description: "Primary 4 weekend program", order: 4 },
      { name: "Primary 5", code: "WC-P5", description: "Primary 5 weekend program", order: 5 },
      { name: "Primary 6", code: "WC-P6", description: "Primary 6 weekend program", order: 6 }
    ]
  },
  {
    name: "Masjid Education Classes",
    code: "MEC",
    description: "Educational classes focused on Islamic studies and Quranic education",
    duration: "12 months",
    fee: 300,
    status: "active",
    levels: [
      { name: "Tahfeez Beginner", code: "MEC-TB", description: "Foundation Islamic studies and basic Quran memorization", order: 1 },
      { name: "Tahfeez Intermediate", code: "MEC-TI", description: "Intermediate Islamic studies and continued Quran memorization", order: 2 },
      { name: "Tahfeez Advanced", code: "MEC-TA", description: "Advanced Islamic studies and comprehensive Quran memorization", order: 3 },
      { name: "Arabic Language", code: "MEC-AL", description: "Specialized Arabic language studies", order: 4 }
    ]
  }
];

// Helper function to check if a course already exists
async function courseExists(code) {
  const coursesRef = collection(db, "courses");
  const q = query(coursesRef, where("code", "==", code));
  const querySnapshot = await getDocs(q);
  return !querySnapshot.empty;
}

// Add a course to Firestore
async function addCourse(course) {
  try {
    // Check if course already exists
    if (await courseExists(course.code)) {
      console.log(`Course ${course.name} (${course.code}) already exists. Skipping.`);
      return null;
    }

    // Add created_at and updated_at timestamps
    const courseData = {
      ...course,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp()
    };

    // Remove levels array before saving course
    const { levels, ...courseToSave } = courseData;

    // Add the course
    const courseRef = await addDoc(collection(db, "courses"), courseToSave);
    console.log(`Added course ${course.name} with ID: ${courseRef.id}`);
    
    return {
      id: courseRef.id,
      levels: levels
    };
  } catch (error) {
    console.error(`Error adding course ${course.name}:`, error);
    return null;
  }
}

// Add a level to Firestore
async function addLevel(level, courseId) {
  try {
    // Add course_id, created_at, and updated_at
    const levelData = {
      ...level,
      course_id: courseId,
      status: "active",
      created_at: serverTimestamp(),
      updated_at: serverTimestamp()
    };

    // Add the level
    const levelRef = await addDoc(collection(db, "levels"), levelData);
    console.log(`Added level ${level.name} with ID: ${levelRef.id} for course ID: ${courseId}`);
    
    return levelRef.id;
  } catch (error) {
    console.error(`Error adding level ${level.name}:`, error);
    return null;
  }
}

// Main function to add all courses and their levels
async function addCoursesAndLevels() {
  console.log("Starting to add courses and levels...");
  
  for (const course of coursesToAdd) {
    // Add the course
    const result = await addCourse(course);
    
    if (result) {
      // Add all levels for this course
      for (const level of result.levels) {
        await addLevel(level, result.id);
      }
    }
  }
  
  console.log("Finished adding courses and levels.");
}

// Execute the main function
addCoursesAndLevels()
  .then(() => {
    console.log("Script completed successfully");
    process.exit(0);
  })
  .catch(error => {
    console.error("Script failed:", error);
    process.exit(1);
  }); 