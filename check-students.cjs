const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs } = require('firebase/firestore');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Get students
async function getStudents() {
  try {
    const studentsRef = collection(db, 'students');
    const snapshot = await getDocs(studentsRef);
    
    console.log(`Found ${snapshot.size} students`);
    
    snapshot.docs.forEach((doc, i) => {
      const data = doc.data();
      console.log(`Student ${i+1}: ID=${doc.id}, Name=${data.first_name || 'N/A'} ${data.last_name || 'N/A'}, Level ID=${data.level_id || 'N/A'}`);
    });
  } catch (error) {
    console.error('Error getting students:', error);
  }
}

getStudents(); 