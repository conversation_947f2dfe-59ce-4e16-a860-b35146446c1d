# Firebase Setup Instructions

This project has been migrated from Supabase to Firebase/Firestore. Follow these steps to set up your Firebase project:

## 1. Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project"
3. Enter a project name and follow the setup wizard
4. Enable Google Analytics if desired

## 2. Set Up Firebase Authentication

1. In the Firebase Console, go to "Authentication"
2. Click "Get started"
3. Enable the "Email/Password" sign-in method
4. Optionally, set up other authentication methods as needed

## 3. Create a Firestore Database

1. In the Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" for initial development
   > Note: Test mode is recommended during development as it provides unrestricted access. You'll need to update to production mode with proper security rules before deploying to production.
4. Select a location for your database

## 4. Set Up Security Rules

1. In the Firestore Database section, go to the "Rules" tab
2. Set up appropriate security rules for your collections. Here's a basic example:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 5. Register a Web App

1. In the Firebase Console, click on the gear icon and select "Project settings"
2. Scroll down to "Your apps" and click the web icon (</>) to add a web app
3. Register your app with a nickname
4. Copy the Firebase configuration object

## 6. Update Environment Variables

Update your `.env` file with the Firebase configuration:

```
# Firebase Configuration
VITE_FIREBASE_API_KEY="your-api-key"
VITE_FIREBASE_AUTH_DOMAIN="your-project-id.firebaseapp.com"
VITE_FIREBASE_PROJECT_ID="your-project-id"
VITE_FIREBASE_STORAGE_BUCKET="your-project-id.appspot.com"
VITE_FIREBASE_MESSAGING_SENDER_ID="your-messaging-sender-id"
VITE_FIREBASE_APP_ID="your-app-id"
VITE_FIREBASE_MEASUREMENT_ID="your-measurement-id"
```

## 7. Create Initial Admin User

To create an initial admin user:

1. Use the Firebase Authentication console to create a user
2. Manually create a document in the "profiles" collection with the following structure:
   ```
   {
     "email": "<EMAIL>",
     "displayName": "Admin User",
     "role": "admin",
     "photoURL": ""
   }
   ```
3. Make sure the document ID matches the user's UID from Firebase Authentication

## 8. Database Structure

The application uses the following collections:

- `profiles`: User profiles
- `students`: Student records
- `courses`: Course information
- `levels`: Course levels
- `activity_logs`: User activity logs
- `transactions`: Financial transactions
- `payments`: Payment records

## 9. Testing

After setting up Firebase, test the application by:

1. Starting the development server: `npm run dev`
2. Navigating to the login page
3. Logging in with your admin credentials
4. Verifying that you can create, read, update, and delete records 