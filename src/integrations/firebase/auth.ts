import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile,
  deleteUser,
  User,
  UserCredential
} from 'firebase/auth';
import { auth } from './client';
import { createWithId, ProfileDocument, getById } from './firestore';

// Sign up a new user
export const signUp = async (
  email: string, 
  password: string, 
  displayName?: string
): Promise<UserCredential> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    
    // Update profile with display name if provided
    if (displayName && userCredential.user) {
      await updateProfile(userCredential.user, { displayName });
    }
    
    // Create a user profile document in Firestore
    if (userCredential.user) {
      await createWithId<Omit<ProfileDocument, 'id'>>('profiles', userCredential.user.uid, {
        email: userCredential.user.email || '',
        displayName: displayName || '',
        role: 'user',
        photoURL: userCredential.user.photoURL || '',
      });
    }
    
    return userCredential;
  } catch (error) {
    console.error('Error signing up:', error);
    throw error;
  }
};

// Sign in an existing user
export const signIn = async (
  email: string, 
  password: string
): Promise<UserCredential> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    
    // Check if user profile exists, if not create one
    if (userCredential.user) {
      try {
        const profileDoc = await getById<ProfileDocument>('profiles', userCredential.user.uid);
        
        // If profile doesn't exist, create a default one
        if (!profileDoc) {
          console.log('Creating default profile for user:', userCredential.user.uid);
          await createWithId<Omit<ProfileDocument, 'id'>>('profiles', userCredential.user.uid, {
            email: userCredential.user.email || '',
            displayName: userCredential.user.displayName || '',
            role: 'admin', // Default role
            photoURL: userCredential.user.photoURL || '',
          });
        }
      } catch (error) {
        console.error('Error checking/creating user profile:', error);
      }
    }
    
    return userCredential;
  } catch (error) {
    console.error('Error signing in:', error);
    throw error;
  }
};

// Sign out the current user
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Reset password
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error resetting password:', error);
    throw error;
  }
};

// Delete a user account
export const deleteUserAccount = async (user: User): Promise<void> => {
  try {
    await deleteUser(user);
  } catch (error) {
    console.error('Error deleting user account:', error);
    throw error;
  }
};

// Get the current user
export const getCurrentUser = (): User | null => {
  return auth.currentUser;
};

// Listen for auth state changes
export const onAuthStateChanged = (callback: (user: User | null) => void): (() => void) => {
  return auth.onAuthStateChanged(callback);
}; 