import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Course extends FirestoreDocument {
  name: string;
  code: string;
  description: string;
  duration: string;
  fee: number;
  status: 'active' | 'inactive';
}

const COLLECTION = 'courses';

// Get all courses
export const getAllCourses = async (filters?: Partial<Course>): Promise<Course[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Course>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting courses:', error);
    throw error;
  }
};

// Get a course by ID
export const getCourseById = async (id: string): Promise<Course | null> => {
  return await db.getById<Course>(COLLECTION, id);
};

// Get a course by code
export const getCourseByCode = async (code: string): Promise<Course | null> => {
  try {
    const courses = await db.getAll<Course>(COLLECTION, [
      whereEqual('code', code)
    ]);
    
    return courses.length > 0 ? courses[0] : null;
  } catch (error) {
    console.error('Error getting course by code:', error);
    throw error;
  }
};

// Create a new course
export const createCourse = async (course: Omit<Course, 'id'>): Promise<string> => {
  return await db.create<Omit<Course, 'id'>>(COLLECTION, course);
};

// Update a course
export const updateCourse = async (
  id: string, 
  course: Partial<Course>
): Promise<void> => {
  // Cast to any to bypass TypeScript's strict type checking
  await db.update<Course>(COLLECTION, id, course as any);
};

// Delete a course
export const deleteCourse = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 