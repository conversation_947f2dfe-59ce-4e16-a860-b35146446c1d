import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Transaction extends FirestoreDocument {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  status: 'completed' | 'pending' | 'cancelled';
  notes?: string;
  payment_id?: string;
  student_id?: string;
}

const COLLECTION = 'transactions';

// Get all transactions
export const getAllTransactions = async (filters?: Partial<Transaction>): Promise<Transaction[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Transaction>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting transactions:', error);
    throw error;
  }
};

// Get a transaction by ID
export const getTransactionById = async (id: string): Promise<Transaction | null> => {
  return await db.getById<Transaction>(COLLECTION, id);
};

// Create a new transaction
export const createTransaction = async (transaction: Omit<Transaction, 'id'>): Promise<string> => {
  return await db.create<Omit<Transaction, 'id'>>(COLLECTION, transaction);
};

// Update a transaction
export const updateTransaction = async (
  id: string, 
  transaction: Partial<Transaction>
): Promise<void> => {
  await db.update<Transaction>(COLLECTION, id, transaction as any);
};

// Delete a transaction
export const deleteTransaction = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
};

// Get transactions by date range
export const getTransactionsByDateRange = async (
  startDate: string,
  endDate: string
): Promise<Transaction[]> => {
  try {
    // Firebase doesn't support range queries directly in the same way as Supabase
    // We'll fetch all transactions and filter in memory
    const allTransactions = await getAllTransactions();
    
    return allTransactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      return transactionDate >= start && transactionDate <= end;
    });
  } catch (error) {
    console.error('Error getting transactions by date range:', error);
    throw error;
  }
};

// Calculate summary statistics
export const calculateTransactionSummary = async (): Promise<{
  totalIncome: number;
  totalExpense: number;
  netBalance: number;
}> => {
  try {
    const transactions = await getAllTransactions();
    
    const totalIncome = transactions
      .filter(t => t.type === 'income' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0);
      
    const totalExpense = transactions
      .filter(t => t.type === 'expense' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0);
      
    const netBalance = totalIncome - totalExpense;
    
    return {
      totalIncome,
      totalExpense,
      netBalance
    };
  } catch (error) {
    console.error('Error calculating transaction summary:', error);
    throw error;
  }
}; 