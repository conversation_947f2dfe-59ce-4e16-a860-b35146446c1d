import * as db from '../firestore';
import { Timestamp } from 'firebase/firestore';

// Collection names
export const CLASS_TIMETABLE_COLLECTION = 'class_timetable';
export const EXAM_TIMETABLE_COLLECTION = 'exam_timetable';

// Base timetable entry interface
export interface BaseTimetableEntry {
  id: string;
  subject: string;
  teacher: string; // Teacher ID
  room: string;
  level: string; // Level ID
  startTime: string;
  endTime: string;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

// Class timetable entry interface
export interface ClassTimetableEntry extends BaseTimetableEntry {
  day: string; // Monday, Tuesday, etc.
}

// Exam timetable entry interface
export interface ExamTimetableEntry extends BaseTimetableEntry {
  examDate: string; // ISO date string format
  examType: string; // Midterm, Final, etc.
}

// Get all class timetable entries
export const getAllClassTimetable = async (): Promise<ClassTimetableEntry[]> => {
  return await db.getAll<ClassTimetableEntry>(CLASS_TIMETABLE_COLLECTION);
};

// Get class timetable entries by level
export const getClassTimetableByLevel = async (levelId: string): Promise<ClassTimetableEntry[]> => {
  return await db.getAll<ClassTimetableEntry>(CLASS_TIMETABLE_COLLECTION, [
    db.whereEqual('level', levelId)
  ]);
};

// Get class timetable entries by day
export const getClassTimetableByDay = async (day: string): Promise<ClassTimetableEntry[]> => {
  return await db.getAll<ClassTimetableEntry>(CLASS_TIMETABLE_COLLECTION, [
    db.whereEqual('day', day)
  ]);
};

// Get class timetable entries by teacher
export const getClassTimetableByTeacher = async (teacherId: string): Promise<ClassTimetableEntry[]> => {
  return await db.getAll<ClassTimetableEntry>(CLASS_TIMETABLE_COLLECTION, [
    db.whereEqual('teacher', teacherId)
  ]);
};

// Get a single class timetable entry by ID
export const getClassTimetableEntry = async (id: string): Promise<ClassTimetableEntry | null> => {
  return await db.getById<ClassTimetableEntry>(CLASS_TIMETABLE_COLLECTION, id);
};

// Create a new class timetable entry
export const createClassTimetableEntry = async (entry: Omit<ClassTimetableEntry, 'id'>): Promise<string> => {
  return await db.create<Omit<ClassTimetableEntry, 'id'>>(CLASS_TIMETABLE_COLLECTION, entry);
};

// Update a class timetable entry
export const updateClassTimetableEntry = async (
  id: string, 
  entry: Partial<Omit<ClassTimetableEntry, 'id' | 'created_at'>>
): Promise<void> => {
  await db.update<Partial<ClassTimetableEntry>>(CLASS_TIMETABLE_COLLECTION, id, entry);
};

// Delete a class timetable entry
export const deleteClassTimetableEntry = async (id: string): Promise<void> => {
  await db.remove(CLASS_TIMETABLE_COLLECTION, id);
};

// Get all exam timetable entries
export const getAllExamTimetable = async (): Promise<ExamTimetableEntry[]> => {
  return await db.getAll<ExamTimetableEntry>(EXAM_TIMETABLE_COLLECTION);
};

// Get exam timetable entries by level
export const getExamTimetableByLevel = async (levelId: string): Promise<ExamTimetableEntry[]> => {
  return await db.getAll<ExamTimetableEntry>(EXAM_TIMETABLE_COLLECTION, [
    db.whereEqual('level', levelId)
  ]);
};

// Get exam timetable entries by date range
export const getExamTimetableByDateRange = async (startDate: string, endDate: string): Promise<ExamTimetableEntry[]> => {
  return await db.getAll<ExamTimetableEntry>(EXAM_TIMETABLE_COLLECTION, [
    db.whereGreaterThanOrEqual('examDate', startDate),
    db.whereLessThanOrEqual('examDate', endDate),
    db.orderByField('examDate', 'asc')
  ]);
};

// Get exam timetable entries by exam type
export const getExamTimetableByType = async (examType: string): Promise<ExamTimetableEntry[]> => {
  return await db.getAll<ExamTimetableEntry>(EXAM_TIMETABLE_COLLECTION, [
    db.whereEqual('examType', examType)
  ]);
};

// Get a single exam timetable entry by ID
export const getExamTimetableEntry = async (id: string): Promise<ExamTimetableEntry | null> => {
  return await db.getById<ExamTimetableEntry>(EXAM_TIMETABLE_COLLECTION, id);
};

// Create a new exam timetable entry
export const createExamTimetableEntry = async (entry: Omit<ExamTimetableEntry, 'id'>): Promise<string> => {
  return await db.create<Omit<ExamTimetableEntry, 'id'>>(EXAM_TIMETABLE_COLLECTION, entry);
};

// Update an exam timetable entry
export const updateExamTimetableEntry = async (
  id: string, 
  entry: Partial<Omit<ExamTimetableEntry, 'id' | 'created_at'>>
): Promise<void> => {
  await db.update<Partial<ExamTimetableEntry>>(EXAM_TIMETABLE_COLLECTION, id, entry);
};

// Delete an exam timetable entry
export const deleteExamTimetableEntry = async (id: string): Promise<void> => {
  await db.remove(EXAM_TIMETABLE_COLLECTION, id);
}; 