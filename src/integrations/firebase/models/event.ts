import * as db from '../firestore';
import { Timestamp } from 'firebase/firestore';

// Collection name
export const COLLECTION = 'events';

// Event interface
export interface Event {
  id: string;
  title: string;
  date: string; // ISO date string format
  startTime: string;
  endTime: string;
  location: string;
  category: string; // 'exam', 'academic', 'meeting', 'holiday', 'cultural', 'sports', etc.
  description: string;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

// Get all events
export const getEvents = async (): Promise<Event[]> => {
  return await db.getAll<Event>(COLLECTION);
};

// Get events by category
export const getEventsByCategory = async (category: string): Promise<Event[]> => {
  return await db.query<Event>(COLLECTION, [
    db.where('category', '==', category)
  ]);
};

// Get events by date range
export const getEventsByDateRange = async (startDate: string, endDate: string): Promise<Event[]> => {
  return await db.query<Event>(COLLECTION, [
    db.where('date', '>=', startDate),
    db.where('date', '<=', endDate),
    db.orderBy('date', 'asc')
  ]);
};

// Get a single event by ID
export const getEvent = async (id: string): Promise<Event | null> => {
  return await db.getById<Event>(COLLECTION, id);
};

// Create a new event
export const createEvent = async (event: Omit<Event, 'id'>): Promise<string> => {
  return await db.create<Omit<Event, 'id'>>(COLLECTION, event);
};

// Update an event
export const updateEvent = async (
  id: string, 
  event: Partial<Event>
): Promise<void> => {
  await db.update<Event>(COLLECTION, id, event as any);
};

// Delete an event
export const deleteEvent = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 