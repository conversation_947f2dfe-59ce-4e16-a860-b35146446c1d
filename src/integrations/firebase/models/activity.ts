import { FirestoreDocument, QueryConstraint, whereEqual, orderByField } from '../firestore';
import * as db from '../firestore';
import { ActivityType } from '@/types/activity';
import { Timestamp } from 'firebase/firestore';

export interface ActivityLog extends FirestoreDocument {
  user_id: string;
  activity_type: ActivityType;
  description: string;
  metadata: Record<string, any>;
  ip_address: string;
  user_agent: string;
}

const COLLECTION = 'activity_logs';

// Get all activity logs
export const getAllActivityLogs = async (filters?: Partial<ActivityLog>): Promise<ActivityLog[]> => {
  try {
    console.log("Getting activity logs with filters:", filters);
    
    const constraints: QueryConstraint[] = [
      orderByField('created_at', 'desc')
    ];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          console.log(`Adding filter: ${key} = ${value}`);
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    const logs = await db.getAll<ActivityLog>(COLLECTION, constraints);
    console.log(`Retrieved ${logs.length} activity logs`);
    
    // Ensure all logs have required fields
    return logs
      .filter(log => {
        // Check for mandatory fields
        if (!log.id || !log.activity_type) {
          console.warn("Filtered out invalid log:", log);
          return false;
        }
        return true;
      })
      .map(log => {
        // Ensure created_at is present in the expected format
        if (!log.created_at) {
          console.warn("Log missing created_at timestamp, adding current time:", log.id);
          // Create a Firestore Timestamp from current date for compatibility
          const currentDate = new Date();
          const timestamp = Timestamp.fromDate(currentDate);
          
          return {
            ...log,
            created_at: timestamp
          };
        }
        return log;
      });
  } catch (error) {
    console.error('Error getting activity logs:', error);
    // Return empty array instead of throwing to prevent UI crashes
    return [];
  }
};

// Get activity logs by user ID
export const getActivityLogsByUserId = async (userId: string): Promise<ActivityLog[]> => {
  try {
    return await db.getAll<ActivityLog>(COLLECTION, [
      whereEqual('user_id', userId),
      orderByField('created_at', 'desc')
    ]);
  } catch (error) {
    console.error('Error getting activity logs by user ID:', error);
    throw error;
  }
};

// Create a new activity log
export const createActivityLog = async (log: Omit<ActivityLog, 'id'>): Promise<string> => {
  return await db.create<Omit<ActivityLog, 'id'>>(COLLECTION, log);
}; 