import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface ExamResult extends FirestoreDocument {
  exam_id: string;
  student_id: string;
  marks: number;
  grade: string;
  remarks?: string;
  date_recorded: string;
  recorded_by: string;
}

const COLLECTION = 'exam_results';

// Get all exam results
export const getAllExamResults = async (filters?: Partial<ExamResult>): Promise<ExamResult[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<ExamResult>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting exam results:', error);
    throw error;
  }
};

// Get exam results by exam ID
export const getExamResultsByExamId = async (examId: string): Promise<ExamResult[]> => {
  try {
    return await db.getAll<ExamResult>(COLLECTION, [
      whereEqual('exam_id', examId)
    ]);
  } catch (error) {
    console.error('Error getting exam results by exam ID:', error);
    throw error;
  }
};

// Get exam results by student ID
export const getExamResultsByStudentId = async (studentId: string): Promise<ExamResult[]> => {
  try {
    return await db.getAll<ExamResult>(COLLECTION, [
      whereEqual('student_id', studentId)
    ]);
  } catch (error) {
    console.error('Error getting exam results by student ID:', error);
    throw error;
  }
};

// Get exam result by exam ID and student ID
export const getExamResultByExamAndStudentId = async (
  examId: string,
  studentId: string
): Promise<ExamResult | null> => {
  try {
    const results = await db.getAll<ExamResult>(COLLECTION, [
      whereEqual('exam_id', examId),
      whereEqual('student_id', studentId)
    ]);
    
    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error('Error getting exam result by exam and student ID:', error);
    throw error;
  }
};

// Create a new exam result
export const createExamResult = async (result: Omit<ExamResult, 'id'>): Promise<string> => {
  return await db.create<Omit<ExamResult, 'id'>>(COLLECTION, result);
};

// Update an exam result
export const updateExamResult = async (
  id: string, 
  result: Partial<ExamResult>
): Promise<void> => {
  await db.update<ExamResult>(COLLECTION, id, result as any);
};

// Delete an exam result
export const deleteExamResult = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
};

// Bulk create exam results
export const bulkCreateExamResults = async (
  results: Omit<ExamResult, 'id'>[]
): Promise<string[]> => {
  const ids: string[] = [];
  
  for (const result of results) {
    const id = await createExamResult(result);
    ids.push(id);
  }
  
  return ids;
}; 