import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  onSnapshot,
  serverTimestamp,
  Timestamp,
  DocumentData,
  Unsubscribe,
  startAfter,
  QueryConstraint
} from 'firebase/firestore';
import { db } from '../client';
import { 
  DissertationDocument, 
  DissertationComment, 
  DissertationVersion,
  StudentActivity,
  DissertationSettings,
  DissertationNotification,
  SupervisorDashboardData 
} from '@/types/dissertation';

// Collections
const DISSERTATIONS_COLLECTION = 'dissertations';
const COMMENTS_COLLECTION = 'dissertation_comments';
const VERSIONS_COLLECTION = 'dissertation_versions';
const ACTIVITIES_COLLECTION = 'student_activities';
const SETTINGS_COLLECTION = 'dissertation_settings';
const NOTIFICATIONS_COLLECTION = 'dissertation_notifications';

// Helper function to convert Firestore timestamps to Date objects
const convertTimestamps = (data: any): any => {
  const converted = { ...data };
  
  // Convert all timestamp fields to Date objects
  const timestampFields = ['created_at', 'updated_at', 'submission_deadline', 'last_viewed_by_supervisor'];
  timestampFields.forEach(field => {
    if (converted[field] && converted[field].toDate) {
      converted[field] = converted[field].toDate();
    }
  });
  
  return converted;
};

// Convert Date objects to Firestore timestamps for saving
const convertDatesToTimestamps = (data: any): any => {
  const converted = { ...data };
  
  // Convert Date objects to server timestamps for new documents
  const dateFields = ['created_at', 'updated_at', 'submission_deadline', 'last_viewed_by_supervisor'];
  dateFields.forEach(field => {
    if (converted[field] instanceof Date) {
      converted[field] = Timestamp.fromDate(converted[field]);
    }
  });
  
  return converted;
};

// DISSERTATION CRUD OPERATIONS

export const dissertationService = {
  // Get all dissertations for a user (student or supervisor)
  async getUserDissertations(userId: string, userRole: 'student' | 'supervisor'): Promise<DissertationDocument[]> {
    try {
      const dissertationsRef = collection(db, DISSERTATIONS_COLLECTION);
      const field = userRole === 'student' ? 'student_id' : 'supervisor_id';
      
      const q = query(
        dissertationsRef,
        where(field, '==', userId)
        // Temporarily removed orderBy to avoid index requirement
        // orderBy('updated_at', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const dissertations = querySnapshot.docs.map(doc => convertTimestamps({ 
        id: doc.id, 
        ...doc.data() 
      })) as DissertationDocument[];
      
      // Sort in memory as a temporary workaround
      return dissertations.sort((a, b) => 
        new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
      );
    } catch (error) {
      console.error('Error getting user dissertations:', error);
      throw error;
    }
  },

  // Get dissertation by ID
  async getDissertationById(id: string): Promise<DissertationDocument | null> {
    try {
      const docRef = doc(db, DISSERTATIONS_COLLECTION, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return convertTimestamps({ id: docSnap.id, ...docSnap.data() }) as DissertationDocument;
      }
      return null;
    } catch (error) {
      console.error('Error getting dissertation:', error);
      throw error;
    }
  },

  // Create new dissertation
  async createDissertation(data: Omit<DissertationDocument, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    try {
      const dissertationsRef = collection(db, DISSERTATIONS_COLLECTION);
      const now = serverTimestamp();
      
      const dissertationData = {
        ...convertDatesToTimestamps(data),
        created_at: now,
        updated_at: now,
        version: 1,
        word_count: 0,
        page_count: 1,
        active_collaborators: [],
        pages_reviewed: [],
        chapters_ready_for_review: []
      };
      
      const docRef = await addDoc(dissertationsRef, dissertationData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating dissertation:', error);
      throw error;
    }
  },

  // Update dissertation
  async updateDissertation(id: string, data: Partial<DissertationDocument>): Promise<void> {
    try {
      const docRef = doc(db, DISSERTATIONS_COLLECTION, id);
      const updateData = {
        ...convertDatesToTimestamps(data),
        updated_at: serverTimestamp()
      };
      
      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.created_at;
      
      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating dissertation:', error);
      throw error;
    }
  },

  // Delete dissertation
  async deleteDissertation(id: string): Promise<void> {
    try {
      const docRef = doc(db, DISSERTATIONS_COLLECTION, id);
      await deleteDoc(docRef);
      
      // Also delete related comments, versions, etc.
      await Promise.all([
        this.deleteAllComments(id),
        this.deleteAllVersions(id),
        this.deleteSettings(id)
      ]);
    } catch (error) {
      console.error('Error deleting dissertation:', error);
      throw error;
    }
  },

  // Real-time listener for dissertation changes
  onDissertationChange(id: string, callback: (dissertation: DissertationDocument | null) => void): Unsubscribe {
    const docRef = doc(db, DISSERTATIONS_COLLECTION, id);
    
    return onSnapshot(docRef, (doc) => {
      if (doc.exists()) {
        const data = convertTimestamps({ id: doc.id, ...doc.data() }) as DissertationDocument;
        callback(data);
      } else {
        callback(null);
      }
    });
  },

  // Get supervisor dashboard data
  async getSupervisorDashboard(supervisorId: string): Promise<SupervisorDashboardData[]> {
    try {
      const dissertationsRef = collection(db, DISSERTATIONS_COLLECTION);
      const q = query(
        dissertationsRef,
        where('supervisor_id', '==', supervisorId)
        // Temporarily removed orderBy to avoid index requirement
        // orderBy('updated_at', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const dashboardData: SupervisorDashboardData[] = [];
      
      // Get all students first to avoid multiple fetches
      const studentsCollection = collection(db, 'profiles');
      const studentsSnapshot = await getDocs(studentsCollection);
      const studentProfiles = new Map();
      
      studentsSnapshot.forEach(doc => {
        studentProfiles.set(doc.id, doc.data());
      });
      
      for (const docSnap of querySnapshot.docs) {
        const dissertation = convertTimestamps({ id: docSnap.id, ...docSnap.data() }) as DissertationDocument;
        
        // Calculate dashboard metrics
        const daysSinceUpdate = Math.floor((Date.now() - dissertation.updated_at.getTime()) / (1000 * 60 * 60 * 24));
        const progressPercentage = Math.round((dissertation.pages_reviewed.length / dissertation.page_count) * 100);
        
        // Get pending comments count
        const pendingComments = await this.getPendingCommentsCount(dissertation.id);
        
        // Get student name from profiles
        const studentProfile = studentProfiles.get(dissertation.student_id);
        const studentName = studentProfile ? studentProfile.displayName : 'Unknown Student';
        
        dashboardData.push({
          student_id: dissertation.student_id,
          student_name: studentName,
          document_id: dissertation.id,
          document_title: dissertation.title,
          last_edited: dissertation.updated_at,
          pages_completed: dissertation.pages_reviewed.length,
          total_pages: dissertation.page_count,
          word_count: dissertation.word_count,
          status: dissertation.status,
          days_since_last_update: daysSinceUpdate,
          pending_comments: pendingComments,
          chapters_ready_for_review: dissertation.chapters_ready_for_review.length,
          progress_percentage: progressPercentage
        });
      }
      
      // Sort in memory as a temporary workaround
      return dashboardData.sort((a, b) => 
        new Date(b.last_edited).getTime() - new Date(a.last_edited).getTime()
      );
    } catch (error) {
      console.error('Error getting supervisor dashboard:', error);
      throw error;
    }
  }
};

// COMMENT OPERATIONS

export const commentService = {
  // Get comments for a dissertation
  async getComments(documentId: string): Promise<DissertationComment[]> {
    try {
      const commentsRef = collection(db, COMMENTS_COLLECTION);
      const q = query(
        commentsRef,
        where('document_id', '==', documentId)
        // Temporarily removed orderBy to avoid index requirement
        // orderBy('created_at', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      const comments = querySnapshot.docs.map(doc => convertTimestamps({ 
        id: doc.id, 
        ...doc.data() 
      })) as DissertationComment[];
      
      // Sort in memory as a temporary workaround
      return comments.sort((a, b) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
    } catch (error) {
      console.error('Error getting comments:', error);
      throw error;
    }
  },

  // Add new comment
  async addComment(comment: Omit<DissertationComment, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    try {
      const commentsRef = collection(db, COMMENTS_COLLECTION);
      const now = serverTimestamp();
      
      const commentData = {
        ...convertDatesToTimestamps(comment),
        created_at: now,
        updated_at: now,
        status: 'active',
        replies: []
      };
      
      const docRef = await addDoc(commentsRef, commentData);
      return docRef.id;
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  },

  // Update comment
  async updateComment(id: string, data: Partial<DissertationComment>): Promise<void> {
    try {
      const docRef = doc(db, COMMENTS_COLLECTION, id);
      const updateData = {
        ...convertDatesToTimestamps(data),
        updated_at: serverTimestamp()
      };
      
      delete updateData.id;
      delete updateData.created_at;
      
      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating comment:', error);
      throw error;
    }
  },

  // Delete comment
  async deleteComment(id: string): Promise<void> {
    try {
      const docRef = doc(db, COMMENTS_COLLECTION, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting comment:', error);
      throw error;
    }
  },

  // Get pending comments count
  async getPendingCommentsCount(documentId: string): Promise<number> {
    try {
      const commentsRef = collection(db, COMMENTS_COLLECTION);
      const q = query(
        commentsRef,
        where('document_id', '==', documentId),
        where('status', '==', 'active')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.size;
    } catch (error) {
      console.error('Error getting pending comments count:', error);
      return 0;
    }
  }
};

// VERSION OPERATIONS

export const versionService = {
  // Get versions for a dissertation
  async getVersions(documentId: string): Promise<DissertationVersion[]> {
    try {
      const versionsRef = collection(db, VERSIONS_COLLECTION);
      const q = query(
        versionsRef,
        where('document_id', '==', documentId)
        // Temporarily removed orderBy to avoid index requirement
        // orderBy('version_number', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const versions = querySnapshot.docs.map(doc => convertTimestamps({ 
        id: doc.id, 
        ...doc.data() 
      })) as DissertationVersion[];
      
      // Sort in memory as a temporary workaround
      return versions.sort((a, b) => b.version_number - a.version_number);
    } catch (error) {
      console.error('Error getting versions:', error);
      throw error;
    }
  },

  // Create new version
  async createVersion(version: Omit<DissertationVersion, 'id' | 'created_at'>): Promise<string> {
    try {
      const versionsRef = collection(db, VERSIONS_COLLECTION);
      
      const versionData = {
        ...convertDatesToTimestamps(version),
        created_at: serverTimestamp()
      };
      
      const docRef = await addDoc(versionsRef, versionData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating version:', error);
      throw error;
    }
  }
};

// ACTIVITY TRACKING

export const activityService = {
  // Log student activity
  async logActivity(activity: Omit<StudentActivity, 'id' | 'timestamp'>): Promise<string> {
    try {
      const activitiesRef = collection(db, ACTIVITIES_COLLECTION);
      
      const activityData = {
        ...activity,
        timestamp: serverTimestamp()
      };
      
      const docRef = await addDoc(activitiesRef, activityData);
      return docRef.id;
    } catch (error) {
      console.error('Error logging activity:', error);
      throw error;
    }
  },

  // Get student activities
  async getStudentActivities(studentId: string, documentId?: string): Promise<StudentActivity[]> {
    try {
      const activitiesRef = collection(db, ACTIVITIES_COLLECTION);
      let q;
      
      if (documentId) {
        // If documentId is provided, query by both student_id and document_id
        q = query(
          activitiesRef,
          where('student_id', '==', studentId),
          where('document_id', '==', documentId)
          // Temporarily removed orderBy to avoid index requirement
          // orderBy('timestamp', 'desc'),
          // limit(50)
        );
      } else {
        // If no documentId, query only by student_id
        q = query(
          activitiesRef,
          where('student_id', '==', studentId)
          // Temporarily removed orderBy to avoid index requirement
          // orderBy('timestamp', 'desc'),
          // limit(50)
        );
      }
      
      const querySnapshot = await getDocs(q);
      const activities = querySnapshot.docs.map(doc => convertTimestamps({ 
        id: doc.id, 
        ...doc.data() 
      })) as StudentActivity[];
      
      // Sort in memory and limit as a temporary workaround
      return activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 50);
    } catch (error) {
      console.error('Error getting student activities:', error);
      throw error;
    }
  }
};

// SETTINGS OPERATIONS

export const settingsService = {
  // Get dissertation settings
  async getSettings(documentId: string): Promise<DissertationSettings | null> {
    try {
      const settingsRef = collection(db, SETTINGS_COLLECTION);
      const q = query(settingsRef, where('document_id', '==', documentId));
      
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        return { id: doc.id, ...doc.data() } as DissertationSettings;
      }
      return null;
    } catch (error) {
      console.error('Error getting settings:', error);
      throw error;
    }
  },

  // Save dissertation settings
  async saveSettings(settings: Omit<DissertationSettings, 'id'>): Promise<string> {
    try {
      const settingsRef = collection(db, SETTINGS_COLLECTION);
      
      // Check if settings already exist
      const existing = await this.getSettings(settings.document_id);
      
      if (existing) {
        // Update existing settings
        const docRef = doc(db, SETTINGS_COLLECTION, existing.id);
        await updateDoc(docRef, settings);
        return existing.id;
      } else {
        // Create new settings
        const docRef = await addDoc(settingsRef, settings);
        return docRef.id;
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  }
};

// NOTIFICATION OPERATIONS

export const notificationService = {
  // Get notifications for a user
  async getUserNotifications(userId: string): Promise<DissertationNotification[]> {
    try {
      const notificationsRef = collection(db, NOTIFICATIONS_COLLECTION);
      const q = query(
        notificationsRef,
        where('recipient_id', '==', userId)
        // Temporarily removed orderBy to avoid index requirement
        // orderBy('created_at', 'desc'),
        // limit(20)
      );
      
      const querySnapshot = await getDocs(q);
      const notifications = querySnapshot.docs.map(doc => convertTimestamps({ 
        id: doc.id, 
        ...doc.data() 
      })) as DissertationNotification[];
      
      // Sort in memory and limit as a temporary workaround
      return notifications
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 20);
    } catch (error) {
      console.error('Error getting notifications:', error);
      throw error;
    }
  },

  // Create notification
  async createNotification(notification: Omit<DissertationNotification, 'id' | 'created_at'>): Promise<string> {
    try {
      const notificationsRef = collection(db, NOTIFICATIONS_COLLECTION);
      
      const notificationData = {
        ...notification,
        created_at: serverTimestamp(),
        read: false
      };
      
      const docRef = await addDoc(notificationsRef, notificationData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  },

  // Mark notification as read
  async markAsRead(id: string): Promise<void> {
    try {
      const docRef = doc(db, NOTIFICATIONS_COLLECTION, id);
      await updateDoc(docRef, { read: true });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }
};

// Helper functions used internally
dissertationService.deleteAllComments = async (documentId: string): Promise<void> => {
  const commentsRef = collection(db, COMMENTS_COLLECTION);
  const q = query(commentsRef, where('document_id', '==', documentId));
  const querySnapshot = await getDocs(q);
  
  const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
  await Promise.all(deletePromises);
};

dissertationService.deleteAllVersions = async (documentId: string): Promise<void> => {
  const versionsRef = collection(db, VERSIONS_COLLECTION);
  const q = query(versionsRef, where('document_id', '==', documentId));
  const querySnapshot = await getDocs(q);
  
  const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
  await Promise.all(deletePromises);
};

dissertationService.deleteSettings = async (documentId: string): Promise<void> => {
  const settingsRef = collection(db, SETTINGS_COLLECTION);
  const q = query(settingsRef, where('document_id', '==', documentId));
  const querySnapshot = await getDocs(q);
  
  const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
  await Promise.all(deletePromises);
};

dissertationService.getPendingCommentsCount = commentService.getPendingCommentsCount;

// Services are already exported individually above 