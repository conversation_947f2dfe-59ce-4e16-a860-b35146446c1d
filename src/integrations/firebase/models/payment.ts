import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Payment extends FirestoreDocument {
  student_id: string;
  amount: number;
  date_paid: string;
  payment_due_date: string;
  status: 'paid' | 'pending' | 'overdue' | 'partial';
  notes?: string;
  course_id?: string;
  level_id?: string;
}

const COLLECTION = 'payments';

// Get all payments
export const getAllPayments = async (filters?: Partial<Payment>): Promise<Payment[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Payment>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting payments:', error);
    throw error;
  }
};

// Get payments by student ID
export const getPaymentsByStudentId = async (studentId: string): Promise<Payment[]> => {
  try {
    return await db.getAll<Payment>(COLLECTION, [
      whereEqual('student_id', studentId)
    ]);
  } catch (error) {
    console.error('Error getting payments by student ID:', error);
    throw error;
  }
};

// Get a payment by ID
export const getPaymentById = async (id: string): Promise<Payment | null> => {
  return await db.getById<Payment>(COLLECTION, id);
};

// Create a new payment
export const createPayment = async (payment: Omit<Payment, 'id'>): Promise<string> => {
  return await db.create<Omit<Payment, 'id'>>(COLLECTION, payment);
};

// Update a payment
export const updatePayment = async (
  id: string, 
  payment: Partial<Payment>
): Promise<void> => {
  await db.update<Payment>(COLLECTION, id, payment as any);
};

// Delete a payment
export const deletePayment = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
};

// Calculate student payment summary
export const calculateStudentPaymentSummary = async (studentId: string): Promise<{
  totalPaid: number;
  paymentStatus: 'paid' | 'pending' | 'overdue' | 'partial';
}> => {
  try {
    const payments = await getPaymentsByStudentId(studentId);
    
    const totalPaid = payments.reduce((sum, payment) => {
      return sum + (payment.status === 'paid' || payment.status === 'partial' ? payment.amount : 0);
    }, 0);
    
    // Determine payment status based on payments
    let paymentStatus: 'paid' | 'pending' | 'overdue' | 'partial' = 'pending';
    
    if (payments.length === 0) {
      paymentStatus = 'pending';
    } else if (payments.some(p => p.status === 'overdue')) {
      paymentStatus = 'overdue';
    } else if (payments.every(p => p.status === 'paid')) {
      paymentStatus = 'paid';
    } else {
      paymentStatus = 'partial';
    }
    
    return {
      totalPaid,
      paymentStatus
    };
  } catch (error) {
    console.error('Error calculating student payment summary:', error);
    throw error;
  }
}; 