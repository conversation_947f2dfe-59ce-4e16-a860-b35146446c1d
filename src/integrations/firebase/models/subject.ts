import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Subject extends FirestoreDocument {
  name: string;
  code: string;
  description: string;
  courseId: string; // Reference to the course this subject belongs to
  teacherId?: string; // Optional reference to the teacher assigned to this subject
  credits: number;
  status: 'active' | 'inactive';
}

const COLLECTION = 'subjects';

// Get all subjects
export const getAllSubjects = async (filters?: Partial<Subject>): Promise<Subject[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Subject>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting subjects:', error);
    throw error;
  }
};

// Get a subject by ID
export const getSubjectById = async (id: string): Promise<Subject | null> => {
  return await db.getById<Subject>(COLLECTION, id);
};

// Get subjects by course ID
export const getSubjectsByCourseId = async (courseId: string): Promise<Subject[]> => {
  try {
    return await db.getAll<Subject>(COLLECTION, [
      whereEqual('courseId', courseId)
    ]);
  } catch (error) {
    console.error('Error getting subjects by course ID:', error);
    throw error;
  }
};

// Get a subject by code
export const getSubjectByCode = async (code: string): Promise<Subject | null> => {
  try {
    const subjects = await db.getAll<Subject>(COLLECTION, [
      whereEqual('code', code)
    ]);
    
    return subjects.length > 0 ? subjects[0] : null;
  } catch (error) {
    console.error('Error getting subject by code:', error);
    throw error;
  }
};

// Create a new subject
export const createSubject = async (subject: Omit<Subject, 'id'>): Promise<string> => {
  return await db.create<Omit<Subject, 'id'>>(COLLECTION, subject);
};

// Update a subject
export const updateSubject = async (
  id: string, 
  subject: Partial<Subject>
): Promise<void> => {
  await db.update<Subject>(COLLECTION, id, subject as any);
};

// Delete a subject
export const deleteSubject = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
};
 