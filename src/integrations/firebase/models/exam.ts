import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Exam extends FirestoreDocument {
  name: string;
  subject: string;
  course_id: string | null;
  level_id: string | null;
  class: string;
  type: string;
  date: string;
  time: string;
  status: string;
}

const COLLECTION = 'exams';

// Get all exams
export const getAllExams = async (filters?: Partial<Exam>): Promise<Exam[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Exam>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting exams:', error);
    throw error;
  }
};

// Get an exam by ID
export const getExamById = async (id: string): Promise<Exam | null> => {
  return await db.getById<Exam>(COLLECTION, id);
};

// Get exams by course ID
export const getExamsByCourseId = async (courseId: string): Promise<Exam[]> => {
  try {
    return await db.getAll<Exam>(COLLECTION, [
      whereEqual('course_id', courseId)
    ]);
  } catch (error) {
    console.error('Error getting exams by course ID:', error);
    throw error;
  }
};

// Get exams by level ID
export const getExamsByLevelId = async (levelId: string): Promise<Exam[]> => {
  try {
    return await db.getAll<Exam>(COLLECTION, [
      whereEqual('level_id', levelId)
    ]);
  } catch (error) {
    console.error('Error getting exams by level ID:', error);
    throw error;
  }
};

// Create a new exam
export const createExam = async (exam: Omit<Exam, 'id'>): Promise<string> => {
  return await db.create<Omit<Exam, 'id'>>(COLLECTION, exam);
};

// Update an exam
export const updateExam = async (
  id: string, 
  exam: Partial<Exam>
): Promise<void> => {
  await db.update<Exam>(COLLECTION, id, exam as any);
};

// Delete an exam
export const deleteExam = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 