import { FirestoreDocument, QueryConstraint, whereEqual, whereGreaterThanOrEqual, whereLessThanOrEqual } from '../firestore';
import * as db from '../firestore';

export interface AttendanceRecord extends FirestoreDocument {
  student_id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
  marked_by: string;
  course_id?: string;
  level_id?: string;
}

const COLLECTION = 'attendance_records';

// Get all attendance records
export const getAllAttendanceRecords = async (filters?: Partial<AttendanceRecord>): Promise<AttendanceRecord[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<AttendanceRecord>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting attendance records:', error);
    throw error;
  }
};

// Get attendance records by date
export const getAttendanceRecordsByDate = async (date: string): Promise<AttendanceRecord[]> => {
  try {
    return await db.getAll<AttendanceRecord>(COLLECTION, [
      whereEqual('date', date)
    ]);
  } catch (error) {
    console.error('Error getting attendance records by date:', error);
    throw error;
  }
};

// Get attendance records by student ID
export const getAttendanceRecordsByStudentId = async (studentId: string): Promise<AttendanceRecord[]> => {
  try {
    return await db.getAll<AttendanceRecord>(COLLECTION, [
      whereEqual('student_id', studentId)
    ]);
  } catch (error) {
    console.error('Error getting attendance records by student ID:', error);
    throw error;
  }
};

// Get attendance records by date and student ID
export const getAttendanceRecordByDateAndStudentId = async (
  date: string,
  studentId: string
): Promise<AttendanceRecord | null> => {
  try {
    const records = await db.getAll<AttendanceRecord>(COLLECTION, [
      whereEqual('date', date),
      whereEqual('student_id', studentId)
    ]);
    
    return records.length > 0 ? records[0] : null;
  } catch (error) {
    console.error('Error getting attendance record by date and student ID:', error);
    throw error;
  }
};

// Get attendance records by date range
export const getAttendanceRecordsByDateRange = async (startDate: string, endDate: string): Promise<AttendanceRecord[]> => {
  try {
    return await db.getAll<AttendanceRecord>(COLLECTION, [
      whereGreaterThanOrEqual('date', startDate),
      whereLessThanOrEqual('date', endDate)
    ]);
  } catch (error) {
    console.error('Error getting attendance records by date range:', error);
    throw error;
  }
};

// Create a new attendance record
export const createAttendanceRecord = async (record: Omit<AttendanceRecord, 'id'>): Promise<string> => {
  return await db.create<Omit<AttendanceRecord, 'id'>>(COLLECTION, record);
};

// Update an attendance record
export const updateAttendanceRecord = async (
  id: string, 
  record: Partial<AttendanceRecord>
): Promise<void> => {
  await db.update<AttendanceRecord>(COLLECTION, id, record as any);
};

// Delete an attendance record
export const deleteAttendanceRecord = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
};

// Bulk create attendance records
export const bulkCreateAttendanceRecords = async (
  records: Omit<AttendanceRecord, 'id'>[]
): Promise<string[]> => {
  const ids: string[] = [];
  
  for (const record of records) {
    const id = await createAttendanceRecord(record);
    ids.push(id);
  }
  
  return ids;
}; 