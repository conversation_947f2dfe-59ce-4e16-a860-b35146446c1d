import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Assignment extends FirestoreDocument {
  title: string;
  description: string;
  level_id: string;
  due_date: string;
  points: number;
  status: string;
  created_by: string;
  created_at: string;
  updated_by?: string;
  updated_at?: string;
}

const COLLECTION = 'assignments';

// Get all assignments
export const getAllAssignments = async (filters?: Partial<Assignment>): Promise<Assignment[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Assignment>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting assignments:', error);
    throw error;
  }
};

// Get an assignment by ID
export const getAssignmentById = async (id: string): Promise<Assignment | null> => {
  return await db.getById<Assignment>(COLLECTION, id);
};

// Get assignments by level ID
export const getAssignmentsByLevelId = async (levelId: string): Promise<Assignment[]> => {
  try {
    return await db.getAll<Assignment>(COLLECTION, [
      whereEqual('level_id', levelId)
    ]);
  } catch (error) {
    console.error('Error getting assignments by level ID:', error);
    throw error;
  }
};

// Get assignments by creator ID
export const getAssignmentsByCreatorId = async (creatorId: string): Promise<Assignment[]> => {
  try {
    return await db.getAll<Assignment>(COLLECTION, [
      whereEqual('created_by', creatorId)
    ]);
  } catch (error) {
    console.error('Error getting assignments by creator ID:', error);
    throw error;
  }
};

// Create a new assignment
export const createAssignment = async (assignment: Omit<Assignment, 'id'>): Promise<string> => {
  return await db.create<Omit<Assignment, 'id'>>(COLLECTION, assignment);
};

// Update an assignment
export const updateAssignment = async (
  id: string, 
  assignment: Partial<Assignment>
): Promise<void> => {
  await db.update<Assignment>(COLLECTION, id, assignment as any);
};

// Delete an assignment
export const deleteAssignment = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 