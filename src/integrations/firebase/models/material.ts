import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Material extends FirestoreDocument {
  title: string;
  description: string;
  level_id: string;
  type: string;
  file_url: string;
  file_name: string;
  file_size: number;
  file_id: string; // Google Drive file ID
  created_by: string;
  created_at: string;
  updated_by?: string;
  updated_at?: string;
}

const COLLECTION = 'materials';

// Get all materials
export const getAllMaterials = async (filters?: Partial<Material>): Promise<Material[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Material>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting materials:', error);
    throw error;
  }
};

// Get a material by ID
export const getMaterialById = async (id: string): Promise<Material | null> => {
  return await db.getById<Material>(COLLECTION, id);
};

// Get materials by level ID
export const getMaterialsByLevelId = async (levelId: string): Promise<Material[]> => {
  try {
    return await db.getAll<Material>(COLLECTION, [
      whereEqual('level_id', levelId)
    ]);
  } catch (error) {
    console.error('Error getting materials by level ID:', error);
    throw error;
  }
};

// Get materials by type
export const getMaterialsByType = async (type: string): Promise<Material[]> => {
  try {
    return await db.getAll<Material>(COLLECTION, [
      whereEqual('type', type)
    ]);
  } catch (error) {
    console.error('Error getting materials by type:', error);
    throw error;
  }
};

// Get materials by creator ID
export const getMaterialsByCreatorId = async (creatorId: string): Promise<Material[]> => {
  try {
    return await db.getAll<Material>(COLLECTION, [
      whereEqual('created_by', creatorId)
    ]);
  } catch (error) {
    console.error('Error getting materials by creator ID:', error);
    throw error;
  }
};

// Create a new material
export const createMaterial = async (material: Omit<Material, 'id'>): Promise<string> => {
  return await db.create<Omit<Material, 'id'>>(COLLECTION, material);
};

// Update a material
export const updateMaterial = async (
  id: string, 
  material: Partial<Material>
): Promise<void> => {
  await db.update<Material>(COLLECTION, id, material as any);
};

// Delete a material
export const deleteMaterial = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
};

export const getMaterials = async (): Promise<Material[]> => {
  const materials = await db.getAll<Material>(COLLECTION);
  // Sort materials by creation date (newest first)
  return materials.sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime());
};

export const getMaterialsByLevel = async (levelId: string): Promise<Material[]> => {
  const constraints: QueryConstraint[] = [
    whereEqual('level_id', levelId)
  ];
  
  const materials = await db.query<Material>(COLLECTION, constraints);
  // Sort materials by creation date (newest first)
  return materials.sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime());
}; 