import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Level extends FirestoreDocument {
  name: string;
  code: string;
  description?: string;
  course_id: string;
  order: number;
  status: 'active' | 'inactive';
}

const COLLECTION = 'levels';

// Get all levels
export const getAllLevels = async (filters?: Partial<Level>): Promise<Level[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Level>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting levels:', error);
    throw error;
  }
};

// Get a level by ID
export const getLevelById = async (id: string): Promise<Level | null> => {
  return await db.getById<Level>(COLLECTION, id);
};

// Get levels by course ID
export const getLevelsByCourseId = async (courseId: string): Promise<Level[]> => {
  try {
    return await db.getAll<Level>(COLLECTION, [
      whereEqual('course_id', courseId)
    ]);
  } catch (error) {
    console.error('Error getting levels by course ID:', error);
    throw error;
  }
};

// Create a new level
export const createLevel = async (level: Omit<Level, 'id'>): Promise<string> => {
  return await db.create<Omit<Level, 'id'>>(COLLECTION, level);
};

// Update a level
export const updateLevel = async (
  id: string, 
  level: Partial<Level>
): Promise<void> => {
  // Cast to any to bypass TypeScript's strict type checking
  await db.update<Level>(COLLECTION, id, level as any);
};

// Delete a level
export const deleteLevel = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 