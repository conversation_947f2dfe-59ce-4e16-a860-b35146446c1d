import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Submission extends FirestoreDocument {
  assignment_id: string;
  student_id: string;
  content?: string;
  file_url?: string;
  file_name?: string;
  file_id?: string; // Google Drive file ID
  submitted_at: string;
  status: 'submitted' | 'graded' | 'late';
  grade?: number;
  feedback?: string;
  graded_at?: string;
  graded_by?: string;
}

const COLLECTION = 'submissions';

// Get all submissions
export const getAllSubmissions = async (filters?: Partial<Submission>): Promise<Submission[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Submission>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting submissions:', error);
    throw error;
  }
};

// Get a submission by ID
export const getSubmissionById = async (id: string): Promise<Submission | null> => {
  return await db.getById<Submission>(COLLECTION, id);
};

// Get submissions by assignment ID
export const getSubmissionsByAssignmentId = async (assignmentId: string): Promise<Submission[]> => {
  try {
    return await db.getAll<Submission>(COLLECTION, [
      whereEqual('assignment_id', assignmentId)
    ]);
  } catch (error) {
    console.error('Error getting submissions by assignment ID:', error);
    throw error;
  }
};

// Get submissions by student ID
export const getSubmissionsByStudentId = async (studentId: string): Promise<Submission[]> => {
  try {
    return await db.getAll<Submission>(COLLECTION, [
      whereEqual('student_id', studentId)
    ]);
  } catch (error) {
    console.error('Error getting submissions by student ID:', error);
    throw error;
  }
};

// Get submissions by assignment ID and student ID
export const getSubmissionsByAssignmentAndStudent = async (
  assignmentId: string,
  studentId: string
): Promise<Submission[]> => {
  try {
    return await db.getAll<Submission>(COLLECTION, [
      whereEqual('assignment_id', assignmentId),
      whereEqual('student_id', studentId)
    ]);
  } catch (error) {
    console.error('Error getting submissions by assignment and student:', error);
    throw error;
  }
};

// Create a new submission
export const createSubmission = async (submission: Omit<Submission, 'id'>): Promise<string> => {
  return await db.create<Omit<Submission, 'id'>>(COLLECTION, submission);
};

// Update a submission
export const updateSubmission = async (
  id: string, 
  submission: Partial<Submission>
): Promise<void> => {
  await db.update<Submission>(COLLECTION, id, submission as any);
};

// Delete a submission
export const deleteSubmission = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 