import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  query, 
  where, 
  orderBy, 
  getDocs,
  Timestamp,
  onSnapshot,
  limit
} from 'firebase/firestore';
import { db } from '../client';

export type Comment = {
  id: string;
  content: string;
  userId: string;
  userName: string;
  userRole: string;
  liveClassId: string;
  createdAt: Date;
  updatedAt: Date;
  isDeleted: boolean;
};

export type CommentInput = Omit<Comment, 'id' | 'createdAt' | 'updatedAt' | 'isDeleted'>;

const COLLECTION_NAME = 'liveClassComments';

export const createComment = async (data: CommentInput): Promise<Comment> => {
  const now = new Date();
  const commentData = {
    ...data,
    createdAt: now,
    updatedAt: now,
    isDeleted: false
  };

  const docRef = await addDoc(collection(db, COLLECTION_NAME), {
    ...commentData,
    createdAt: Timestamp.fromDate(now),
    updatedAt: Timestamp.fromDate(now)
  });

  return {
    id: docRef.id,
    ...commentData
  };
};

export const updateComment = async (id: string, content: string): Promise<void> => {
  const now = new Date();
  await updateDoc(doc(db, COLLECTION_NAME, id), {
    content,
    updatedAt: Timestamp.fromDate(now)
  });
};

export const deleteComment = async (id: string): Promise<void> => {
  const now = new Date();
  await updateDoc(doc(db, COLLECTION_NAME, id), {
    isDeleted: true,
    updatedAt: Timestamp.fromDate(now)
  });
};

export const hardDeleteComment = async (id: string): Promise<void> => {
  await deleteDoc(doc(db, COLLECTION_NAME, id));
};

export const getComments = async (
  liveClassId: string, 
  options: { 
    limit?: number, 
    includeDeleted?: boolean 
  } = {}
): Promise<Comment[]> => {
  let q = collection(db, COLLECTION_NAME);
  
  const constraints = [
    where('liveClassId', '==', liveClassId)
  ];
  
  if (!options.includeDeleted) {
    constraints.push(where('isDeleted', '==', false));
  }
  
  constraints.push(orderBy('createdAt', 'desc'));
  
  if (options.limit) {
    constraints.push(limit(options.limit));
  }
  
  q = query(q, ...constraints);
  
  const snapshot = await getDocs(q);
  
  return snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      ...data,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate()
    } as Comment;
  });
};

// Function to subscribe to comments for real-time updates
export const subscribeComments = (
  liveClassId: string,
  options: { 
    limit?: number, 
    includeDeleted?: boolean 
  } = {},
  callback: (comments: Comment[]) => void
) => {
  let q = collection(db, COLLECTION_NAME);
  
  const constraints = [
    where('liveClassId', '==', liveClassId)
  ];
  
  if (!options.includeDeleted) {
    constraints.push(where('isDeleted', '==', false));
  }
  
  constraints.push(orderBy('createdAt', 'desc'));
  
  if (options.limit) {
    constraints.push(limit(options.limit));
  }
  
  q = query(q, ...constraints);
  
  return onSnapshot(q, (snapshot) => {
    const comments = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate()
      } as Comment;
    });
    
    callback(comments);
  });
}; 