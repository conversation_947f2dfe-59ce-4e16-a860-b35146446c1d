import { FirestoreDocument, QueryConstraint, whereEqual } from '../firestore';
import * as db from '../firestore';

export interface Student extends FirestoreDocument {
  student_id: string;
  name: string;
  date_of_birth: string;
  date_of_registration: string;
  nationality: string;
  gender: string;
  address: string;
  mobile_number: string;
  whatsapp_number: string;
  parent_name: string;
  parent_mobile: string;
  parent_whatsapp: string;
  parent_email: string;
  parent_occupation: string;
  passport_picture: string | null;
  course_id: string;
  level_id: string;
  enrollment_status: string;
  payment_status?: string;
}

const COLLECTION = 'students';

// Get all students
export const getAllStudents = async (filters?: Partial<Student>): Promise<Student[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Student>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting students:', error);
    throw error;
  }
};

// Get a student by ID
export const getStudentById = async (id: string): Promise<Student | null> => {
  return await db.getById<Student>(COLLECTION, id);
};

// Get a student by student_id
export const getStudentByStudentId = async (studentId: string): Promise<Student | null> => {
  try {
    const students = await db.getAll<Student>(COLLECTION, [
      whereEqual('student_id', studentId)
    ]);
    
    return students.length > 0 ? students[0] : null;
  } catch (error) {
    console.error('Error getting student by student_id:', error);
    throw error;
  }
};

// Create a new student
export const createStudent = async (student: Omit<Student, 'id'>): Promise<string> => {
  return await db.create<Omit<Student, 'id'>>(COLLECTION, student);
};

// Update a student
export const updateStudent = async (
  id: string, 
  student: Partial<Student>
): Promise<void> => {
  // Cast to any to bypass TypeScript's strict type checking
  // This is safe because Firestore will only update the fields that are provided
  await db.update<Student>(COLLECTION, id, student as any);
};

// Delete a student
export const deleteStudent = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 