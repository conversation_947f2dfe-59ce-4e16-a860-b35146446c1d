import { FirestoreDocument, QueryConstraint, whereEqual, whereArrayContains } from '../firestore';
import * as db from '../firestore';

export interface Announcement extends FirestoreDocument {
  title: string;
  content: string;
  target_levels: string[];
  priority: string;
  expires_at: string;
  created_by: string;
  created_at: string;
  author: string;
  updated_by?: string;
  updated_at?: string;
}

const COLLECTION = 'announcements';

// Get all announcements
export const getAllAnnouncements = async (filters?: Partial<Announcement>): Promise<Announcement[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters) {
      // Add filters as constraints
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          constraints.push(whereEqual(key, value));
        }
      });
    }
    
    return await db.getAll<Announcement>(COLLECTION, constraints);
  } catch (error) {
    console.error('Error getting announcements:', error);
    throw error;
  }
};

// Get an announcement by ID
export const getAnnouncementById = async (id: string): Promise<Announcement | null> => {
  return await db.getById<Announcement>(COLLECTION, id);
};

// Get announcements by target level
export const getAnnouncementsByTargetLevel = async (levelId: string): Promise<Announcement[]> => {
  try {
    return await db.getAll<Announcement>(COLLECTION, [
      whereArrayContains('target_levels', levelId)
    ]);
  } catch (error) {
    console.error('Error getting announcements by target level:', error);
    throw error;
  }
};

// Get announcements by creator ID
export const getAnnouncementsByCreatorId = async (creatorId: string): Promise<Announcement[]> => {
  try {
    return await db.getAll<Announcement>(COLLECTION, [
      whereEqual('created_by', creatorId)
    ]);
  } catch (error) {
    console.error('Error getting announcements by creator ID:', error);
    throw error;
  }
};

// Create a new announcement
export const createAnnouncement = async (announcement: Omit<Announcement, 'id'>): Promise<string> => {
  return await db.create<Omit<Announcement, 'id'>>(COLLECTION, announcement);
};

// Update an announcement
export const updateAnnouncement = async (
  id: string, 
  announcement: Partial<Announcement>
): Promise<void> => {
  await db.update<Announcement>(COLLECTION, id, announcement as any);
};

// Delete an announcement
export const deleteAnnouncement = async (id: string): Promise<void> => {
  await db.remove(COLLECTION, id);
}; 