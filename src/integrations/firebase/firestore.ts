import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  DocumentData, 
  QueryConstraint, 
  serverTimestamp, 
  Timestamp,
  setDoc
} from 'firebase/firestore';
import { db } from './client';

// Re-export QueryConstraint type and functions
export type { QueryConstraint };
export { query, where };

// Generic type for Firestore documents
export interface FirestoreDocument {
  id: string;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

// Profile document type
export interface ProfileDocument extends FirestoreDocument {
  email: string;
  displayName: string;
  role: string;
  photoURL: string;
  studentId?: string;
}

// Add timestamps to document data
const addTimestamps = (data: DocumentData, isNew = false): DocumentData => {
  const now = serverTimestamp();
  return {
    ...data,
    updated_at: now,
    ...(isNew ? { created_at: now } : {})
  };
};

// Get all documents from a collection
export const getAll = async <T extends FirestoreDocument>(
  collectionName: string,
  constraints: QueryConstraint[] = []
): Promise<T[]> => {
  try {
    const collectionRef = collection(db, collectionName);
    const q = constraints.length > 0 
      ? query(collectionRef, ...constraints) 
      : query(collectionRef);
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({ 
      id: doc.id, 
      ...doc.data() 
    })) as T[];
  } catch (error) {
    console.error(`Error getting documents from ${collectionName}:`, error);
    throw error;
  }
};

// Get a document by ID
export const getById = async <T extends FirestoreDocument>(
  collectionName: string,
  id: string
): Promise<T | null> => {
  try {
    const docRef = doc(db, collectionName, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as T;
    }
    return null;
  } catch (error) {
    console.error(`Error getting document ${id} from ${collectionName}:`, error);
    throw error;
  }
};

// Create a new document
export const create = async <T extends Omit<FirestoreDocument, 'id'>>(
  collectionName: string,
  data: T
): Promise<string> => {
  try {
    const collectionRef = collection(db, collectionName);
    const docWithTimestamps = addTimestamps(data as DocumentData, true);
    const docRef = await addDoc(collectionRef, docWithTimestamps);
    return docRef.id;
  } catch (error) {
    console.error(`Error creating document in ${collectionName}:`, error);
    throw error;
  }
};

// Create a document with a specific ID
export const createWithId = async <T extends Omit<FirestoreDocument, 'id'>>(
  collectionName: string,
  id: string,
  data: T
): Promise<void> => {
  try {
    const docRef = doc(db, collectionName, id);
    const docWithTimestamps = addTimestamps(data as DocumentData, true);
    await setDoc(docRef, docWithTimestamps);
  } catch (error) {
    console.error(`Error creating document with ID ${id} in ${collectionName}:`, error);
    throw error;
  }
};

// Update a document
export const update = async <T extends Partial<FirestoreDocument>>(
  collectionName: string,
  id: string,
  data: Omit<T, 'id' | 'created_at'>
): Promise<void> => {
  try {
    const docRef = doc(db, collectionName, id);
    const docWithTimestamps = addTimestamps(data as DocumentData);
    await updateDoc(docRef, docWithTimestamps);
  } catch (error) {
    console.error(`Error updating document ${id} in ${collectionName}:`, error);
    throw error;
  }
};

// Delete a document
export const remove = async (
  collectionName: string,
  id: string
): Promise<void> => {
  try {
    const docRef = doc(db, collectionName, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error(`Error deleting document ${id} from ${collectionName}:`, error);
    throw error;
  }
};

// Query builder helpers
export const whereEqual = (field: string, value: any) => where(field, '==', value);
export const whereIn = (field: string, values: any[]) => where(field, 'in', values);
export const whereGreaterThan = (field: string, value: any) => where(field, '>', value);
export const whereLessThan = (field: string, value: any) => where(field, '<', value);
export const whereGreaterThanOrEqual = (field: string, value: any) => where(field, '>=', value);
export const whereLessThanOrEqual = (field: string, value: any) => where(field, '<=', value);
export const whereArrayContains = (field: string, value: any) => where(field, 'array-contains', value);
export const orderByField = (field: string, direction: 'asc' | 'desc' = 'asc') => orderBy(field, direction);
export const limitTo = (n: number) => limit(n); 