import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject, 
  listAll, 
  getMetadata 
} from 'firebase/storage';
import { storage } from './client';
import { logActivity } from '@/utils/activity-logger';

// Define folder paths
export const FOLDERS = {
  PROFILE_PHOTOS: 'profile_photos',
  MATERIALS: 'materials',
  SUBMISSIONS: 'submissions',
};

// Interface for file metadata
export interface StorageFileMetadata {
  id: string;
  name: string;
  contentType: string;
  downloadUrl: string;
  fullPath: string;
  size?: number;
  createdTime?: string;
  updatedTime?: string;
}

/**
 * Gets the storage path by combining folder segments
 */
const getStoragePath = (folderPath: string, fileName?: string): string => {
  // Clean the path and ensure no double slashes
  const cleanPath = folderPath.split('/').filter(segment => segment.trim() !== '').join('/');
  return fileName ? `${cleanPath}/${fileName}` : cleanPath;
};

/**
 * Upload a file to Firebase Storage
 */
export const uploadFile = async (
  file: File | Blob | Buffer,
  fileName: string,
  folderPath: string,
  contentType?: string
): Promise<StorageFileMetadata> => {
  try {
    const fullPath = getStoragePath(folderPath, fileName);
    const storageRef = ref(storage, fullPath);
    
    // Convert file to Blob or Buffer for upload
    let fileToUpload: Blob | Uint8Array;
    if (file instanceof File || file instanceof Blob) {
      fileToUpload = file;
    } else {
      fileToUpload = file;  // Buffer is already Uint8Array compatible
    }
    
    // Set content type if provided
    const metadata = contentType ? { contentType } : undefined;
    
    // Upload file
    const snapshot = await uploadBytes(storageRef, fileToUpload, metadata);
    
    // Get download URL
    const downloadUrl = await getDownloadURL(snapshot.ref);
    
    // Get metadata
    const fileMetadata = await getMetadata(snapshot.ref);
    
    await logActivity('file_uploaded_to_storage', {
      fileName,
      folderPath,
      fullPath
    });
    
    return {
      id: snapshot.ref.name,
      name: fileName,
      contentType: fileMetadata.contentType || 'application/octet-stream',
      downloadUrl,
      fullPath: fileMetadata.fullPath,
      size: fileMetadata.size,
      createdTime: fileMetadata.timeCreated,
      updatedTime: fileMetadata.updated
    };
  } catch (error) {
    console.error(`Error uploading file ${fileName}:`, error);
    throw new Error(`Failed to upload file: ${fileName}`);
  }
};

/**
 * Delete a file from Firebase Storage
 */
export const deleteFile = async (filePath: string): Promise<void> => {
  try {
    const storageRef = ref(storage, filePath);
    await deleteObject(storageRef);
    
    await logActivity('file_deleted_from_storage', {
      filePath
    });
  } catch (error) {
    console.error(`Error deleting file ${filePath}:`, error);
    throw new Error(`Failed to delete file: ${filePath}`);
  }
};

/**
 * Get file metadata from Firebase Storage
 */
export const getFileMetadata = async (filePath: string): Promise<StorageFileMetadata> => {
  try {
    const storageRef = ref(storage, filePath);
    const fileMetadata = await getMetadata(storageRef);
    const downloadUrl = await getDownloadURL(storageRef);
    
    return {
      id: storageRef.name,
      name: fileMetadata.name || storageRef.name,
      contentType: fileMetadata.contentType || 'application/octet-stream',
      downloadUrl,
      fullPath: fileMetadata.fullPath,
      size: fileMetadata.size,
      createdTime: fileMetadata.timeCreated,
      updatedTime: fileMetadata.updated
    };
  } catch (error) {
    console.error(`Error getting file metadata for ${filePath}:`, error);
    throw new Error(`Failed to get file metadata: ${filePath}`);
  }
};

/**
 * List files in a folder
 */
export const listFiles = async (folderPath: string): Promise<StorageFileMetadata[]> => {
  try {
    const folderRef = ref(storage, getStoragePath(folderPath));
    const fileList = await listAll(folderRef);
    
    // Get metadata for each file
    const metadataPromises = fileList.items.map(async (itemRef) => {
      try {
        const metadata = await getMetadata(itemRef);
        const downloadUrl = await getDownloadURL(itemRef);
        
        return {
          id: itemRef.name,
          name: metadata.name || itemRef.name,
          contentType: metadata.contentType || 'application/octet-stream',
          downloadUrl,
          fullPath: metadata.fullPath,
          size: metadata.size,
          createdTime: metadata.timeCreated,
          updatedTime: metadata.updated
        };
      } catch (error) {
        console.error(`Error getting metadata for ${itemRef.fullPath}:`, error);
        return null;
      }
    });
    
    const results = await Promise.all(metadataPromises);
    return results.filter((item): item is StorageFileMetadata => item !== null);
  } catch (error) {
    console.error(`Error listing files in folder ${folderPath}:`, error);
    throw new Error(`Failed to list files in folder: ${folderPath}`);
  }
};

/**
 * Get download URL for a file
 */
export const getFileUrl = async (filePath: string): Promise<string> => {
  try {
    const storageRef = ref(storage, filePath);
    return await getDownloadURL(storageRef);
  } catch (error) {
    console.error(`Error getting download URL for ${filePath}:`, error);
    throw new Error(`Failed to get download URL: ${filePath}`);
  }
}; 