import { useState } from 'react';
import { User } from 'firebase/auth';
import { ProfileDocument } from '@/integrations/firebase/firestore';

export function useAuthState() {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<ProfileDocument | null>(null);
  // Initialize loading as true since we need to check auth state when the app loads
  const [isLoading, setIsLoading] = useState<boolean>(false);

  return {
    user,
    setUser,
    userProfile,
    setUserProfile,
    isLoading,
    setIsLoading,
  };
}
