import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/integrations/firebase/client';

export const useMaintenanceMode = () => {
  const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<any>(null);
  const { user } = useAuth();

  useEffect(() => {
    // Function to fetch user profile
    const fetchUserProfile = async () => {
      try {
        if (user) {
          // Try to get profile from Firebase first
          try {
            // Use type assertion to avoid TypeScript errors since the User type can be from different auth providers
            const userId = (user as any).uid || (user as any).id || '';
            const docRef = doc(db, 'profiles', userId);
            const docSnap = await getDoc(docRef);
            
            if (docSnap.exists()) {
              setUserProfile(docSnap.data());
              return;
            }
          } catch (firebaseError) {
            console.error('Error fetching Firebase user profile:', firebaseError);
          }
          
          // Fall back to Supabase
          // Use type assertion to avoid TypeScript errors
          const userId = (user as any).uid || (user as any).id || '';
          const { data, error } = await supabase
            .from('profiles')
            .select('role')
            .eq('id', userId)
            .single();
          
          if (error) {
            console.error('Error fetching user profile from Supabase:', error);
            return;
          }
          
          setUserProfile(data);
        }
      } catch (error) {
        console.error('Error in fetchUserProfile:', error);
      } 
    };
    
    // Function to fetch maintenance mode setting
    const fetchMaintenanceMode = async () => {
      try {
        // First check localStorage for immediate value
        const storedSettings = localStorage.getItem('generalSettings');
        if (storedSettings) {
          const settings = JSON.parse(storedSettings);
          setIsMaintenanceMode(settings.maintenanceMode || false);
        }
        
        // Then try to get the latest from Firebase
        try {
          const docRef = doc(db, 'settings', 'general');
          const docSnap = await getDoc(docRef);
          
          if (docSnap.exists() && docSnap.data().maintenance_mode !== undefined) {
            setIsMaintenanceMode(docSnap.data().maintenance_mode);
            
            // Update localStorage for consistency
            const localSettings = {
              schoolName: docSnap.data().school_name || 'Academic Dashboard',
              maintenanceMode: docSnap.data().maintenance_mode
            };
            localStorage.setItem('generalSettings', JSON.stringify(localSettings));
          }
        } catch (firebaseError) {
          console.error('Error fetching maintenance mode from Firebase:', firebaseError);
        }
      } catch (error) {
        console.error('Error fetching maintenance mode:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Run both functions
    fetchUserProfile();
    fetchMaintenanceMode();
    
    // Set up storage listener
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'generalSettings') {
        try {
          const newSettings = JSON.parse(e.newValue || '{}');
          setIsMaintenanceMode(newSettings.maintenanceMode || false);
        } catch (error) {
          console.error('Error parsing settings from localStorage:', error);
        }
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [user]);

  // Determine if user is a super admin
  const isSuperAdmin = userProfile?.role === 'super_admin';
  
  // Only block users with finance_admin and enrollment_admin roles
  const isBlocked = isMaintenanceMode && 
    (userProfile?.role === 'finance_admin' || userProfile?.role === 'enrollment_admin');

  return {
    isMaintenanceMode,
    isLoading,
    isBlocked,
    isSuperAdmin
  };
};
