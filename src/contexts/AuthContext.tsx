import React, { createContext, useContext, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { User } from 'firebase/auth';
import { useAuthState } from '@/hooks/useAuthState';
import { toast } from 'sonner';
import { auth } from '@/integrations/firebase/client';
import { getById } from '@/integrations/firebase/firestore';
import { ProfileDocument } from '@/integrations/firebase/firestore';
import { signOut } from '@/integrations/firebase/auth';
import { UserRole } from '@/types/user';
import { handleError } from '@/utils/error-handling';

interface AuthContextType {
  user: User | null;
  userProfile: ProfileDocument | null;
  isLoading: boolean;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const {
    user,
    setUser,
    userProfile,
    setUserProfile,
    isLoading,
    setIsLoading
  } = useAuthState();
  const navigate = useNavigate();
  const location = useLocation();

  // Initialize auth state
  useEffect(() => {
    let mounted = true;
    console.log('AuthProvider initializing...');

    async function fetchUserProfile(userId: string) {
      try {
        const profile = await getById<ProfileDocument>('profiles', userId);
        return profile;
      } catch (error) {
        console.error('Error fetching user profile:', error);
        throw error;
      }
    }

    // Set up auth state change listener
    const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
      console.log('Auth state changed:', firebaseUser ? 'SIGNED_IN' : 'SIGNED_OUT');
      
      if (!mounted) return;
      
      setIsLoading(true);
      
      try {
        if (firebaseUser) {
          setUser(firebaseUser);
          
          try {
            const profile = await fetchUserProfile(firebaseUser.uid);
            if (mounted) {
              setUserProfile(profile);
              console.log('Profile set:', profile);
              
              // Only navigate if we're on the login page
              if (location.pathname === '/login') {
                // Determine where to redirect based on user role
                let redirectPath = '/dashboard';
                
                // If user is a student, redirect to student dashboard
                if (profile?.role === 'student') {
                  redirectPath = '/dashboard/student';
                }
                
                const from = (location.state as any)?.from || redirectPath;
                navigate(from, { replace: true });
              }
            }
          } catch (error) {
            console.error('Error fetching profile:', error);
            if (mounted) {
              toast.error(handleError(error, 'Unable to load user profile'));
              setUserProfile(null);
            }
          }
        } else {
          setUser(null);
          setUserProfile(null);
          
          if (location.pathname !== '/login') {
            navigate('/login', { replace: true });
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          toast.error(handleError(error, 'Authentication error occurred'));
          navigate('/login', { replace: true });
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    });

    // Cleanup subscription on unmount
    return () => {
      console.log('AuthProvider cleanup...');
      mounted = false;
      unsubscribe();
    };
  }, [navigate, location.pathname, setUser, setUserProfile, setIsLoading]);

  const logout = async () => {
    try {
      await signOut();
      setUser(null);
      setUserProfile(null);
      navigate('/login', { replace: true });
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error(handleError(error, 'Error logging out'));
    }
  };

  const value = {
    user,
    userProfile,
    isLoading,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
