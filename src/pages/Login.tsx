import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { toast } from 'sonner';
import { User, Lock } from 'lucide-react';
import { auth } from '@/integrations/firebase/client';
import { signIn } from '@/integrations/firebase/auth';
import { handleError } from '@/utils/error-handling';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check if user is already logged in
    const checkSession = async () => {
      const currentUser = auth.currentUser;
      
      if (currentUser) {
        console.log('Active session found, redirecting...');
        navigate('/dashboard', { replace: true });
      }
    };
    
    checkSession();
  }, [navigate, location]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading || !email || !password) return;
    
    setIsLoading(true);
    
    try {
      const userCredential = await signIn(
        email.trim(),
        password.trim()
      );

      if (userCredential.user) {
        console.log('Login successful:', userCredential.user);
        toast.success('Login successful');
        // Use replace to prevent back navigation to login
        navigate('/dashboard', { replace: true });
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast.error(handleError(error, 'Login failed. Please try again.'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-400 via-yellow-50 to-green-100">
      <div className="flex w-full max-w-4xl bg-white/80 backdrop-blur-md shadow-xl rounded-2xl overflow-hidden">
        {/* Left side with login.svg */}
        <div className="hidden md:flex md:w-1/2 bg-green-50 items-center justify-center p-8">
          <img 
            src="/login.svg" 
            alt="Login Illustration" 
            className="w-full max-w-md"
            style={{ filter: 'drop-shadow(0px 4px 6px rgba(0, 100, 0, 0.1))' }}
          />
        </div>
        
        {/* Right side with login form */}
        <Card className="w-full md:w-1/2 p-8 bg-white/90 border-0">
          <div className="text-center mb-8">
            <img src="/logo.svg" alt="PTECH Logo" className="w-20 h-20 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-green-800 mb-2">Welcome Back</h1>
            <p className="text-green-600">Sign in to continue</p>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-700 h-5 w-5" />
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email"
                required
                disabled={isLoading}
                className="pl-10 bg-green-50/70 border-green-100 focus:border-green-500"
              />
            </div>
            
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-700 h-5 w-5" />
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                required
                disabled={isLoading}
                className="pl-10 bg-green-50/70 border-green-100 focus:border-green-500"
              />
            </div>

            <Button 
              type="submit" 
              className="w-full bg-green-700 hover:bg-green-800 text-white"
              disabled={isLoading || !email || !password}
            >
              {isLoading ? 'Signing in...' : 'Login'}
            </Button>
          </form>

          <p className="text-center text-sm text-green-600 mt-8">
            Powered by PTech Innovation
          </p>
        </Card>
      </div>
    </div>
  );
};

export default Login;
