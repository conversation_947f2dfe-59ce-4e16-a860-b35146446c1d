import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Users, Circle, Eye, UserPlus, Copy } from 'lucide-react';
import { CollaborationCursor } from '@/types/dissertation';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { useDissertations } from '@/hooks/useDissertations';
import { arrayUnion } from 'firebase/firestore';

interface CollaborationPanelProps {
  collaborators: CollaborationCursor[];
  documentId: string;
  currentUser: any;
}

export const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  collaborators,
  documentId,
  currentUser
}) => {
  const [showInvite, setShowInvite] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const { updateDissertation } = useDissertations();
  
  // Register user as active collaborator when component mounts
  useEffect(() => {
    if (currentUser?.uid && documentId) {
      const registerAsActive = async () => {
        try {
          await updateDissertation(documentId, {
            active_collaborators: arrayUnion(currentUser.uid)
          });
        } catch (error) {
          console.error('Failed to register as active collaborator:', error);
        }
      };
      
      registerAsActive();
      
      // Cleanup when component unmounts
      return () => {
        const unregisterAsActive = async () => {
          try {
            await updateDissertation(documentId, {
              active_collaborators: collaborators.filter(c => c.user_id !== currentUser.uid).map(c => c.user_id)
            });
          } catch (error) {
            console.error('Failed to unregister as active collaborator:', error);
          }
        };
        
        unregisterAsActive();
      };
    }
  }, [currentUser?.uid, documentId, updateDissertation]);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleColor = (role: 'student' | 'supervisor') => {
    return role === 'supervisor' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800';
  };

  const handleCopyLink = () => {
    const link = `${window.location.origin}/dissertation/${documentId}`;
    navigator.clipboard.writeText(link);
    toast.success("Link copied to clipboard");
  };

  const handleSendInvite = () => {
    if (!inviteEmail.trim()) {
      toast.error("Please enter an email address");
      return;
    }
    
    // This would normally call a function to send an invitation
    toast.success(`Invitation sent to ${inviteEmail}`);
    setInviteEmail('');
    setShowInvite(false);
  };

  return (
    <div className="collaboration-panel h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <h3 className="font-semibold flex items-center mb-4">
          <Users className="w-4 h-4 mr-2" />
          Collaboration ({collaborators.length})
        </h3>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={handleCopyLink}
          >
            <Copy className="w-3 h-3 mr-1" />
            Copy Link
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={() => setShowInvite(!showInvite)}
          >
            <UserPlus className="w-3 h-3 mr-1" />
            Invite
          </Button>
        </div>

        {showInvite && (
          <div className="mt-3 flex space-x-2">
            <Input
              placeholder="Enter email address"
              value={inviteEmail}
              onChange={(e) => setInviteEmail(e.target.value)}
              className="flex-1 h-8 text-sm"
            />
            <Button size="sm" onClick={handleSendInvite}>
              Send
            </Button>
          </div>
        )}
      </div>

      {/* Active Collaborators */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {collaborators.length > 0 ? (
            collaborators.map((collaborator) => (
              <div key={collaborator.user_id} className="flex items-start space-x-3 p-3 bg-white border rounded-lg">
                <div className="relative">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src="" alt={collaborator.user_name} />
                    <AvatarFallback className="text-xs">
                      {getInitials(collaborator.user_name)}
                    </AvatarFallback>
                  </Avatar>
                  <Circle 
                    className="absolute -bottom-1 -right-1 w-3 h-3 fill-current" 
                    style={{ color: collaborator.color }}
                  />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-sm font-medium truncate">
                      {collaborator.user_id === currentUser?.uid ? `${collaborator.user_name} (You)` : collaborator.user_name}
                    </h4>
                    <Badge className={`text-xs ${getRoleColor(collaborator.user_role)}`}>
                      {collaborator.user_role}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <Eye className="w-3 h-3" />
                    <span>
                      Active {formatDistanceToNow(collaborator.last_updated, { addSuffix: true })}
                    </span>
                  </div>
                  
                  {collaborator.selection && (
                    <div className="mt-2 text-xs text-gray-600">
                      <span className="bg-gray-100 px-2 py-1 rounded">
                        Selected text (pos: {collaborator.selection.from}-{collaborator.selection.to})
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No active collaborators</p>
              <p className="text-sm">Share the document to collaborate</p>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Collaboration Info */}
      <div className="p-4 border-t bg-gray-50">
        <div className="text-xs text-gray-600 space-y-1">
          <p>• Real-time collaboration enabled</p>
          <p>• Changes are synced automatically</p>
          <p>• Cursor positions are shared</p>
          <p>• Current role: {currentUser?.role === 'teacher' ? 'Supervisor' : 'Student'}</p>
        </div>
      </div>
    </div>
  );
};
