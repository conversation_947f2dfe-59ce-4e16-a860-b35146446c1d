import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { getCourses } from "@/api/courses";
import { getLevels } from "@/api/levels";
import { createStudent } from "@/api/students";
import { Course } from "@/integrations/firebase/models/course";
import { Loader2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { StudentImageUpload } from "./components/StudentImageUpload";
import { PersonalInformationForm } from "./components/PersonalInformationForm";
import { ContactInformationForm } from "./components/ContactInformationForm";
import { Student } from "@/integrations/firebase/models/student";
import * as studentModel from "@/integrations/firebase/models/student";

export const AddNewStudent = () => {
  const navigate = useNavigate();
  const [studentDetails, setStudentDetails] = useState({
    name: "",
    student_id: "",
    date_of_birth: "",
    gender: "",
    course_id: "",
    level_id: "",
    address: "",
    mobile_number: "",
    whatsapp_number: "",
    parent_name: "",
    parent_mobile: "",
    parent_whatsapp: "",
    parent_email: "",
    parent_occupation: "",
    enrollment_status: "Active",
    date_of_registration: new Date().toISOString().split('T')[0],
    passport_picture: "",
    nationality: "",
  });

  const [courses, setCourses] = useState<Course[]>([]);
  const [levels, setLevels] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const coursesData = await getCourses();
        setCourses(coursesData as Course[]);
        
        const levelsData = await getLevels();
        setLevels(levelsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load required data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  const handleImageChange = (url: string) => {
    setStudentDetails(prev => ({
      ...prev,
      passport_picture: url
    }));
    setImagePreview(url);
  };

  const handleStudentDetailsChange = (field: string, value: string) => {
    setStudentDetails(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Check if student ID already exists
      const existingStudent = await studentModel.getStudentByStudentId(studentDetails.student_id);
      if (existingStudent) {
        throw new Error("A student with this ID already exists. Please use a different ID.");
      }

      // Use the Firebase API to create a student
      await createStudent(studentDetails);
      
      toast.success("Student added successfully!");
      navigate("/dashboard/enrollment");
    } catch (err) {
      console.error('Error adding student:', err);
      toast.error(err instanceof Error ? err.message : "Failed to add student");
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredLevels = levels.filter(level => 
    !studentDetails.course_id || level.course_id === studentDetails.course_id
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Add New Student</h2>
        <p className="text-muted-foreground">Register a new student</p>
      </div>

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <StudentImageUpload
                imagePreview={imagePreview}
                onImageChange={handleImageChange}
              />

              <PersonalInformationForm
                studentDetails={studentDetails}
                onStudentDetailsChange={handleStudentDetailsChange}
                courses={courses}
                filteredLevels={filteredLevels}
              />

              <ContactInformationForm
                studentDetails={studentDetails}
                onStudentDetailsChange={handleStudentDetailsChange}
              />
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Registering...
                </>
              ) : (
                "Register Student"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
