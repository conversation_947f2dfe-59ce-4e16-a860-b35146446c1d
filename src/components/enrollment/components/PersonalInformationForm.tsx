import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Course } from "@/integrations/firebase/models/course";
import { toast } from "sonner";
import { useState, useEffect } from "react";
import * as studentModel from "@/integrations/firebase/models/student";

interface PersonalInformationFormProps {
  studentDetails: any;
  onStudentDetailsChange: (field: string, value: string) => void;
  courses: Course[];
  filteredLevels: any[];
}

export const PersonalInformationForm = ({
  studentDetails,
  onStudentDetailsChange,
  courses,
  filteredLevels,
}: PersonalInformationFormProps) => {
  const [isCheckingId, setIsCheckingId] = useState(false);

  // Validation for name: only allow letters, spaces and some special characters
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow letters, spaces, and special characters typically used in names
    if (/^[a-zA-Z\s\-'.]*$/.test(value) || value === '') {
      onStudentDetailsChange("name", value);
    } else {
      toast.error("Name can only contain letters, spaces, hyphens, apostrophes, and periods");
    }
  };

  // Validation for student ID: only allow numbers and check for duplicates
  const handleStudentIdChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Only allow numeric characters
    if ((/^\d*$/.test(value) || value === '')) {
      onStudentDetailsChange("student_id", value);
      
      // If value has at least 3 characters, check if it already exists
      if (value.length >= 3) {
        setIsCheckingId(true);
        try {
          const existingStudent = await studentModel.getStudentByStudentId(value);
          if (existingStudent) {
            toast.error("This Student ID already exists. Please use a different ID.");
          }
        } catch (error) {
          console.error("Error checking student ID:", error);
        } finally {
          setIsCheckingId(false);
        }
      }
    } else {
      toast.error("Student ID can only contain numbers");
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Full Name</Label>
        <Input
          id="name"
          value={studentDetails.name}
          onChange={handleNameChange}
          placeholder="Enter full name"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="student_id">Student ID</Label>
        <Input
          id="student_id"
          value={studentDetails.student_id}
          onChange={handleStudentIdChange}
          placeholder="Enter student ID"
          required
        />
        {isCheckingId && <p className="text-xs text-muted-foreground">Checking if ID is available...</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="date_of_birth">Date of Birth</Label>
        <Input
          id="date_of_birth"
          type="date"
          value={studentDetails.date_of_birth}
          onChange={(e) => onStudentDetailsChange("date_of_birth", e.target.value)}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="gender">Gender</Label>
        <Select
          value={studentDetails.gender}
          onValueChange={(value) => onStudentDetailsChange("gender", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select gender" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="male">Male</SelectItem>
            <SelectItem value="female">Female</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="nationality">Nationality</Label>
        <Input
          id="nationality"
          value={studentDetails.nationality}
          onChange={(e) => onStudentDetailsChange("nationality", e.target.value)}
          placeholder="Enter nationality"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="course">Course</Label>
        <Select
          value={studentDetails.course_id}
          onValueChange={(value) => {
            onStudentDetailsChange("course_id", value);
            onStudentDetailsChange("level_id", "");
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select course" />
          </SelectTrigger>
          <SelectContent>
            {courses.map((course) => (
              <SelectItem key={course.id} value={course.id}>
                {course.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="level">Level</Label>
        <Select
          value={studentDetails.level_id}
          onValueChange={(value) => onStudentDetailsChange("level_id", value)}
          disabled={!studentDetails.course_id}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select level" />
          </SelectTrigger>
          <SelectContent>
            {filteredLevels.map((level) => (
              <SelectItem key={level.id} value={level.id}>
                {level.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
