import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";
import { logActivity } from "@/utils/activity-logger";
import { uploadFile, FOLDERS } from "@/integrations/firebase/storage";

interface StudentImageUploadProps {
  imagePreview: string | null;
  onImageChange: (url: string) => void;
}

export const StudentImageUpload = ({ imagePreview, onImageChange }: StudentImageUploadProps) => {
  const [isUploading, setIsUploading] = useState(false);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || isUploading) return;

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size should be less than 2MB');
      return;
    }

    // Validate file type
    if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
      toast.error('Only JPG and PNG images are allowed');
      return;
    }

    try {
      setIsUploading(true);
      
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${fileExt}`;

      // Upload to Firebase Storage
      const folderPath = FOLDERS.PROFILE_PHOTOS;
      const uploadedFile = await uploadFile(file, fileName, folderPath, file.type);
      
      // Get the public URL
      const publicUrl = uploadedFile.downloadUrl;
      
      onImageChange(publicUrl);
      
      await logActivity('student_image_uploaded', {
        description: 'Student image uploaded successfully',
        fileName: fileName,
        fileId: uploadedFile.id,
        filePath: uploadedFile.fullPath
      });

      toast.success('Image uploaded successfully');
    } catch (error: any) {
      console.error('Error uploading image:', error);
      toast.error(error.message || 'Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
      const input = document.getElementById('passport_picture') as HTMLInputElement;
      if (input) input.value = '';
    }
  };

  return (
    <div className="col-span-full">
      <Label htmlFor="passport_picture">Passport Picture</Label>
      <div className="mt-2 flex items-center gap-x-3">
        {imagePreview && (
          <div className="relative w-32 h-32">
            <img
              src={imagePreview}
              alt="Preview"
              className="w-full h-full object-cover rounded-lg"
            />
          </div>
        )}
        <Button
          type="button"
          variant="outline"
          className="relative"
          disabled={isUploading}
        >
          {isUploading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Upload Photo
            </>
          )}
          <input
            id="passport_picture"
            name="passport_picture"
            type="file"
            accept="image/*"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            onChange={handleFileChange}
            disabled={isUploading}
          />
        </Button>
      </div>
    </div>
  );
};
