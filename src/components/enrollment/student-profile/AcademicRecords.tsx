import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableRow,
} from "@/components/ui/table"
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import * as examModel from '@/integrations/firebase/models/exam';
import * as examResultModel from '@/integrations/firebase/models/exam-result';

interface AcademicRecordsProps {
  studentId: string;
}

// Define interface for exam results with exam data
interface ExamResultWithExam extends examResultModel.ExamResult {
  exam?: {
    name: string;
    subject: string;
    date: string;
    type: string;
  };
}

export const AcademicRecords = ({ studentId }: AcademicRecordsProps) => {
  const { data: academicRecords, isLoading } = useQuery<ExamResultWithExam[]>({
    queryKey: ['academic-records', studentId],
    queryFn: async () => {
      try {
        // Get exam results from Firebase
        const results = await examResultModel.getExamResultsByStudentId(studentId);
        console.log(`Found ${results.length} exam results for student ${studentId}`);
        
        // Get exam details for each result
        const resultsWithExams = await Promise.all(
          results.map(async (result) => {
            try {
              const exam = await examModel.getExamById(result.exam_id);
              return {
                ...result,
                exam: exam ? {
                  name: exam.name,
                  subject: exam.subject,
                  date: exam.date,
                  type: exam.type
                } : undefined
              } as ExamResultWithExam;
            } catch (error) {
              console.error(`Error fetching exam details for exam ID ${result.exam_id}:`, error);
              return result as ExamResultWithExam;
            }
          })
        );

        return resultsWithExams;
      } catch (error) {
        console.error('Error fetching exam results:', error);
        return [];
      }
    },
    enabled: !!studentId
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <Table>
      <TableCaption>Academic records for student {studentId}</TableCaption>
      <TableHead>
        <TableRow>
          <TableHead>Subject</TableHead>
          <TableHead>Exam</TableHead>
          <TableHead>Marks</TableHead>
          <TableHead>Grade</TableHead>
          <TableHead>Date</TableHead>
        </TableRow>
      </TableHead>
      <TableBody>
        {academicRecords && academicRecords.length > 0 ? (
          academicRecords.map((record) => (
            <TableRow key={record.id}>
              <TableCell>{record.exam?.subject}</TableCell>
              <TableCell>{record.exam?.name}</TableCell>
              <TableCell>{record.marks}/100</TableCell>
              <TableCell>{record.grade}</TableCell>
              <TableCell>{new Date(record.exam?.date || record.date_recorded).toLocaleDateString()}</TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={5} className="text-center text-muted-foreground">
              No academic records found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};
