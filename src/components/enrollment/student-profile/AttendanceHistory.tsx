import { useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { getStudentAttendance } from '@/api/attendance';
import { onSnapshot, collection, query, where } from 'firebase/firestore';
import { db } from '@/integrations/firebase/client';
import { cn } from '@/lib/utils';

interface AttendanceHistoryProps {
  studentId: string;
}

interface AttendanceRecord {
  id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
  marked_by: string;
}

export const AttendanceHistory = ({ studentId }: AttendanceHistoryProps) => {
  const queryClient = useQueryClient();

  // Set up real-time listener for attendance records
  useEffect(() => {
    if (!studentId) return;

    console.log('Setting up real-time attendance listener for student:', studentId);
    const q = query(
      collection(db, 'attendance_records'),
      where('student_id', '==', studentId)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      console.log('Attendance data updated');
      queryClient.invalidateQueries({ queryKey: ['student-attendance', studentId] });
    });

    return () => unsubscribe();
  }, [studentId, queryClient]);

  const { data: attendanceRecords, isLoading } = useQuery<AttendanceRecord[]>({
    queryKey: ['student-attendance', studentId],
    queryFn: () => getStudentAttendance(studentId),
    enabled: !!studentId
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'present':
        return 'bg-green-100 text-green-800';
      case 'absent':
        return 'bg-red-100 text-red-800';
      case 'late':
        return 'bg-yellow-100 text-yellow-800';
      case 'excused':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate attendance statistics
  const totalDays = attendanceRecords?.length || 0;
  const presentDays = attendanceRecords?.filter(record => record.status === 'present').length || 0;
  const absentDays = attendanceRecords?.filter(record => record.status === 'absent').length || 0;
  const lateDays = attendanceRecords?.filter(record => record.status === 'late').length || 0;
  const excusedDays = attendanceRecords?.filter(record => record.status === 'excused').length || 0;
  
  const attendanceRate = totalDays > 0 ? (presentDays / totalDays) * 100 : 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Attendance Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-gray-500">Total Days</p>
              <p className="text-2xl font-bold">{totalDays}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">Present</p>
              <p className="text-2xl font-bold text-green-600">{presentDays}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">Absent</p>
              <p className="text-2xl font-bold text-red-600">{absentDays}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-gray-500">Attendance Rate</p>
              <p className="text-2xl font-bold">{Math.round(attendanceRate)}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Attendance History</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {attendanceRecords && attendanceRecords.length > 0 ? (
                attendanceRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>{formatDate(record.date)}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={cn(getStatusColor(record.status))}>
                        {record.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{record.notes || '-'}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} className="text-center text-gray-500">
                    No attendance records found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}; 