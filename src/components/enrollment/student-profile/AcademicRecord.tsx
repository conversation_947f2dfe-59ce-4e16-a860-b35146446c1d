import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2, ChevronDown, ChevronUp, Filter, Clock, Search } from "lucide-react";
import { onSnapshot, collection, query, where } from 'firebase/firestore';
import { db } from '@/integrations/firebase/client';
import { cn } from '@/lib/utils';
import * as examModel from '@/integrations/firebase/models/exam';
import * as examResultModel from '@/integrations/firebase/models/exam-result';
import { getStudentGrades, getStudentGradeStats, GradeItem } from '@/api/student-grades';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";

interface AcademicRecordProps {
  studentId: string;
}

interface ExamResult {
  id: string;
  exam_id: string;
  student_id: string;
  marks: number;
  grade: string;
  remarks?: string;
  date_recorded: string;
  exam?: {
    name: string;
    subject: string;
    type: string;
    date: string;
  };
}

export const AcademicRecord = ({ studentId }: AcademicRecordProps) => {
  const queryClient = useQueryClient();
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState("");
  
  // Set up real-time listener for exam results
  useEffect(() => {
    if (!studentId) return;

    console.log('Setting up real-time exam results listener for student:', studentId);
    const q = query(
      collection(db, 'exam_results'),
      where('student_id', '==', studentId)
    );

    const unsubscribe = onSnapshot(q, (_snapshot) => {
      console.log('Exam results updated for student:', studentId);
      queryClient.invalidateQueries({ queryKey: ['student-exam-results', studentId] });
    });

    return () => unsubscribe();
  }, [studentId, queryClient]);

  const { data: examResults = [], isLoading } = useQuery<ExamResult[]>({
    queryKey: ['student-exam-results', studentId],
    queryFn: async () => {
      console.log('Fetching exam results for student ID:', studentId);
      
      try {
        // Get exam results
        const results = await examResultModel.getExamResultsByStudentId(studentId);
        console.log(`Found ${results.length} exam results for student ${studentId}`);
        
        // Get exam details for each result
        const resultsWithExams = await Promise.all(
          results.map(async (result) => {
            try {
              const exam = await examModel.getExamById(result.exam_id);
              return {
                ...result,
                exam: exam ? {
                  name: exam.name,
                  subject: exam.subject,
                  type: exam.type,
                  date: exam.date
                } : undefined
              };
            } catch (error) {
              console.error(`Error fetching exam details for exam ID ${result.exam_id}:`, error);
              return result;
            }
          })
        );

        return resultsWithExams;
      } catch (error) {
        console.error('Error fetching exam results:', error);
        return [];
      }
    },
    enabled: !!studentId
  });

  // Add grades query
  const { 
    data: grades = [], 
    isLoading: isLoadingGrades 
  } = useQuery({
    queryKey: ['student-grades', studentId],
    queryFn: () => getStudentGrades(studentId),
    enabled: !!studentId
  });

  // Add grade stats query
  const { 
    data: gradeStats,
    isLoading: isLoadingGradeStats 
  } = useQuery({
    queryKey: ['student-grade-stats', studentId],
    queryFn: () => getStudentGradeStats(studentId),
    enabled: !!studentId
  });

  if (isLoading || isLoadingGrades || isLoadingGradeStats) {
    return (
      <div className="flex items-center justify-center h-48">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade.toUpperCase()) {
      case 'A':
        return 'bg-green-100 text-green-800';
      case 'B':
        return 'bg-blue-100 text-blue-800';
      case 'C':
        return 'bg-yellow-100 text-yellow-800';
      case 'D':
        return 'bg-orange-100 text-orange-800';
      case 'F':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate academic statistics
  const totalExams = examResults.length;
  const averageMarks = totalExams > 0 ? examResults.reduce((sum, result) => sum + result.marks, 0) / totalExams : 0;
  const passedExams = examResults.filter(result => result.grade !== 'F').length;
  const passRate = totalExams > 0 ? (passedExams / totalExams) * 100 : 0;

  // Get unique exam types
  const examTypes = ['all', ...new Set(examResults.map(result => result.exam?.type).filter(Boolean))] as string[];

  // Calculate grade distribution
  const gradeDistribution = {
    A: examResults.filter(r => r.grade.toUpperCase() === 'A').length,
    B: examResults.filter(r => r.grade.toUpperCase() === 'B').length,
    C: examResults.filter(r => r.grade.toUpperCase() === 'C').length,
    D: examResults.filter(r => r.grade.toUpperCase() === 'D').length,
    F: examResults.filter(r => r.grade.toUpperCase() === 'F').length,
  };

  // Calculate best and worst subjects
  const subjectPerformance = examResults.reduce((acc, result) => {
    const subject = result.exam?.subject;
    if (subject) {
      if (!acc[subject]) {
        acc[subject] = { total: 0, count: 0 };
      }
      acc[subject].total += result.marks;
      acc[subject].count += 1;
    }
    return acc;
  }, {} as Record<string, { total: number; count: number }>);

  const subjectAverages = Object.entries(subjectPerformance).map(([subject, data]) => ({
    subject,
    average: data.total / data.count
  })).sort((a, b) => b.average - a.average);

  const bestSubject = subjectAverages[0];
  const worstSubject = subjectAverages[subjectAverages.length - 1];

  // Filter and sort exam results
  const filteredResults = examResults
    .filter(result => filterType === 'all' || result.exam?.type === filterType)
    .sort((a, b) => {
      const dateA = new Date(a.exam?.date || a.date_recorded);
      const dateB = new Date(b.exam?.date || b.date_recorded);
      return sortOrder === 'asc' ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
    });

  // Filter grades based on search and type
  const filteredGrades = grades.filter(grade => {
    const matchesFilter = 
      filterType === 'all' || 
      grade.type === filterType;
    
    const matchesSearch = 
      grade.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (grade.course_name && grade.course_name.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesFilter && matchesSearch;
  }).sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return sortOrder === 'asc' ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
  });

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Academic Overview</TabsTrigger>
          <TabsTrigger value="results">Exam Results</TabsTrigger>
          <TabsTrigger value="grades">All Grades</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 pt-4">
          {totalExams > 0 ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Academic Performance</CardTitle>
                  <CardDescription>Overall academic metrics based on {totalExams} exams</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-1 bg-slate-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-500">Total Exams</p>
                      <p className="text-2xl font-bold">{totalExams}</p>
                    </div>
                    <div className="space-y-1 bg-slate-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-500">Average Marks</p>
                      <p className="text-2xl font-bold">{Math.round(averageMarks)}%</p>
                    </div>
                    <div className="space-y-1 bg-slate-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-500">Passed Exams</p>
                      <p className="text-2xl font-bold text-green-600">{passedExams}</p>
                    </div>
                    <div className="space-y-1 bg-slate-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-500">Pass Rate</p>
                      <p className="text-2xl font-bold">{Math.round(passRate)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Grade Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>A</span>
                          <span>{gradeDistribution.A} exams</span>
                        </div>
                        <Progress value={(gradeDistribution.A / totalExams) * 100} className="h-2 bg-slate-200" />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>B</span>
                          <span>{gradeDistribution.B} exams</span>
                        </div>
                        <Progress value={(gradeDistribution.B / totalExams) * 100} className="h-2 bg-slate-200" />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>C</span>
                          <span>{gradeDistribution.C} exams</span>
                        </div>
                        <Progress value={(gradeDistribution.C / totalExams) * 100} className="h-2 bg-slate-200" />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>D</span>
                          <span>{gradeDistribution.D} exams</span>
                        </div>
                        <Progress value={(gradeDistribution.D / totalExams) * 100} className="h-2 bg-slate-200" />
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>F</span>
                          <span>{gradeDistribution.F} exams</span>
                        </div>
                        <Progress value={(gradeDistribution.F / totalExams) * 100} className="h-2 bg-slate-200" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Subject Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {bestSubject && (
                        <div className="space-y-2">
                          <p className="text-md font-medium text-gray-700">Best Subject</p>
                          <div className="bg-green-50 p-4 rounded-lg">
                            <p className="text-lg font-semibold text-green-800">{bestSubject.subject}</p>
                            <p className="text-sm text-green-600">Average: {Math.round(bestSubject.average)}%</p>
                          </div>
                        </div>
                      )}

                      {worstSubject && (
                        <div className="space-y-2">
                          <p className="text-md font-medium text-gray-700">Needs Improvement</p>
                          <div className="bg-amber-50 p-4 rounded-lg">
                            <p className="text-lg font-semibold text-amber-800">{worstSubject.subject}</p>
                            <p className="text-sm text-amber-600">Average: {Math.round(worstSubject.average)}%</p>
                          </div>
                        </div>
                      )}

                      {subjectAverages.length > 0 && (
                        <div className="pt-2">
                          <p className="text-md font-medium text-gray-700 mb-2">All Subjects</p>
                          <div className="max-h-40 overflow-y-auto pr-2">
                            {subjectAverages.map(({ subject, average }) => (
                              <div key={subject} className="flex justify-between items-center mb-2 text-sm">
                                <span>{subject}</span>
                                <span className="font-medium">{Math.round(average)}%</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Loader2 className="h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-xl font-medium text-gray-700 mb-2">No Academic Records</h3>
                <p className="text-gray-500 text-center max-w-md">
                  This student doesn't have any exam results recorded yet. Academic performance metrics will appear here once exam results are added.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="results" className="space-y-6 pt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between p-4">
              <div>
                <CardTitle>Exam Results</CardTitle>
                <CardDescription>List of all exam results for this student</CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                {examTypes.length > 1 && (
                  <div className="flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-gray-500" />
                    <Select value={filterType} onValueChange={setFilterType}>
                      <SelectTrigger className="h-8 w-[110px]">
                        <SelectValue placeholder="Filter" />
                      </SelectTrigger>
                      <SelectContent>
                        {examTypes.map(type => (
                          <SelectItem key={type} value={type}>
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
                <button
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900"
                >
                  <Clock className="h-4 w-4" />
                  <span>{sortOrder === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}</span>
                </button>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <Loader2 className="h-12 w-12 text-gray-300 mb-4 animate-spin" />
                  <h3 className="text-xl font-medium text-gray-700 mb-2">Loading exam results...</h3>
                </div>
              ) : filteredResults.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Exam</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Subject</TableHead>
                      <TableHead>Marks</TableHead>
                      <TableHead>Grade</TableHead>
                      <TableHead>Remarks</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredResults.map((result) => (
                      <TableRow key={result.id}>
                        <TableCell>{formatDate(result.exam?.date || result.date_recorded)}</TableCell>
                        <TableCell>{result.exam?.name || 'Unknown Exam'}</TableCell>
                        <TableCell>
                          {result.exam?.type ? (
                            <Badge variant="outline" className="bg-slate-100">
                              {result.exam.type}
                            </Badge>
                          ) : '-'}
                        </TableCell>
                        <TableCell>{result.exam?.subject || '-'}</TableCell>
                        <TableCell className="font-medium">{result.marks}%</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={cn(getGradeColor(result.grade))}>
                            {result.grade}
                          </Badge>
                        </TableCell>
                        <TableCell>{result.remarks || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <h3 className="text-xl font-medium text-gray-700 mb-2">No Exam Results Found</h3>
                  <p className="text-gray-500 text-center max-w-md">
                    {filterType !== 'all' 
                      ? `No ${filterType} exam results have been recorded for this student yet.`
                      : 'No exam results have been recorded for this student yet. Once exam results are added, they will appear here.'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="grades" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Grade History</CardTitle>
              <CardDescription>
                Comprehensive view of all graded assignments and exams
              </CardDescription>
              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search grades..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="flex gap-2">
                  <Select value={sortOrder} onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Sort by date" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="desc">Newest First</SelectItem>
                      <SelectItem value="asc">Oldest First</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="assignment">Assignments</SelectItem>
                      <SelectItem value="exam">Exams</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {filteredGrades.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No grades found</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    {filterType !== "all" 
                      ? `No ${filterType}s found. Try changing the filter.` 
                      : searchTerm 
                        ? "No grades match your search. Try a different search term." 
                        : "No graded items available yet."}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredGrades.map((grade) => (
                    <div key={grade.id} className="p-4 border rounded-lg">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{grade.title}</h3>
                            <Badge variant="outline">
                              {grade.type === "assignment" ? "Assignment" : "Exam"}
                            </Badge>
                          </div>
                          {grade.course_name && (
                            <p className="text-sm text-muted-foreground mt-1">
                              Course: {grade.course_name}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                            <span className="text-sm">{formatDate(grade.date)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">
                              {grade.grade} / {grade.max_points}
                            </span>
                            <Badge className={getGradeColor(grade.percentage >= 90 ? 'A' : 
                              grade.percentage >= 80 ? 'B' : 
                              grade.percentage >= 70 ? 'C' : 
                              grade.percentage >= 60 ? 'D' : 'F')}>
                              {grade.percentage.toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <Progress 
                        value={grade.percentage} 
                        className={`h-2 mt-3 ${getGradeColor(
                          grade.percentage >= 90 ? 'A' : 
                          grade.percentage >= 80 ? 'B' : 
                          grade.percentage >= 70 ? 'C' : 
                          grade.percentage >= 60 ? 'D' : 'F'
                        )}`}
                      />
                      
                      {grade.feedback && (
                        <div className="mt-3">
                          <p className="text-sm font-medium">Feedback:</p>
                          <p className="text-sm mt-1">{grade.feedback}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AcademicRecord; 