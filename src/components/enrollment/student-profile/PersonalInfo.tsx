import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { toast } from "sonner";
import type { Student } from "@/types/student";
import { getStudentAttendance } from "@/api/attendance";
import { getStudentPayments, getStudentPaymentSummary } from "@/api/payments";
import * as examModel from '@/integrations/firebase/models/exam';
import * as examResultModel from '@/integrations/firebase/models/exam-result';
import * as courseModel from '@/integrations/firebase/models/course';
import * as levelModel from '@/integrations/firebase/models/level';
import * as XLSX from 'xlsx';

interface PersonalInfoProps {
  student: Student;
}

// Define interfaces for the data types
interface AttendanceRecord {
  date: string;
  status: string;
  notes?: string;
}

interface AttendanceData {
  Date?: string;
  Status?: string;
  Notes?: string;
  Note?: string;
  Error?: string;
}

interface Payment {
  date_paid: string;
  amount: number;
  payment_due_date: string;
  status: string;
  notes?: string;
}

interface PaymentData {
  "Date Paid"?: string;
  Amount?: number;
  "Due Date"?: string;
  Status?: string;
  Notes?: string;
  Note?: string;
  Error?: string;
}

interface FirestoreDocument {
  id: string;
}

interface ExamResult extends FirestoreDocument {
  exam_id: string;
  student_id: string;
  marks: number;
  grade: string;
  remarks?: string;
  date_recorded: string;
  recorded_by: string;
}

interface AcademicData {
  "Exam Name"?: string;
  Subject?: string;
  Date?: string;
  Type?: string;
  Marks?: number | string;
  Grade?: string;
  Remarks?: string;
  Note?: string;
  Error?: string;
}

export const PersonalInfo = ({ student }: PersonalInfoProps) => {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (e) {
      return dateString;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Personal Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex justify-center">
          <Avatar className="h-24 w-24">
            <AvatarImage src={student.passport_picture || ''} alt={student.name} />
            <AvatarFallback>{student.name.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Student ID</p>
            <p>{student.student_id}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Full Name</p>
            <p>{student.name}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Course</p>
            <p>{student.course?.name || 'Not Assigned'}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Level</p>
            <p>{student.level?.name || 'Not Assigned'}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Enrollment Status</p>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              student.enrollment_status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
            }`}>
              {student.enrollment_status}
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Date of Birth</p>
            <p>{new Date(student.date_of_birth).toLocaleDateString()}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Nationality</p>
            <p>{student.nationality || 'Not provided'}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">Registration Date</p>
            <p>{new Date(student.date_of_registration).toLocaleDateString()}</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="border-t pt-4">
            <h3 className="font-medium mb-2">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">Mobile Number</p>
                <p>{student.mobile_number || 'Not provided'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">WhatsApp</p>
                <p>{student.whatsapp_number || 'Not provided'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">Address</p>
                <p>{student.address || 'Not provided'}</p>
              </div>
            </div>
          </div>

          <div className="border-t pt-4">
            <h3 className="font-medium mb-2">Parent/Guardian Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">Parent Name</p>
                <p>{student.parent_name || 'Not provided'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">Parent Mobile</p>
                <p>{student.parent_mobile || 'Not provided'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">Parent WhatsApp</p>
                <p>{student.parent_whatsapp || 'Not provided'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">Parent Email</p>
                <p>{student.parent_email || 'Not provided'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-500">Parent Occupation</p>
                <p>{student.parent_occupation || 'Not provided'}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
