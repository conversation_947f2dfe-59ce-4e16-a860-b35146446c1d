import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Plus, Calendar, ClockIcon, CheckCircle2 } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { CreateExamForm } from './CreateExamForm';
import { ExamList } from './ExamList';
import { getExams } from '@/api/exams';
import type { Exam } from '@/integrations/firebase/models/exam';
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

export const ExamDashboard = () => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");

  const { data: exams, isLoading } = useQuery({
    queryKey: ['exams'],
    queryFn: async () => {
      try {
        console.log('Fetching exams for dashboard');
        return await getExams();
      } catch (error) {
        console.error('Error fetching exams:', error);
        return [];
      }
    }
  });

  const upcomingExams = exams?.filter(exam => new Date(exam.date) > new Date()) || [];
  const completedExams = exams?.filter(exam => new Date(exam.date) <= new Date()) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Exam Management</h2>
          <p className="text-muted-foreground">Manage and track student examinations</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create New Exam
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Exam</DialogTitle>
            </DialogHeader>
            <CreateExamForm onSuccess={() => setIsCreateDialogOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Total Exams</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700">{exams?.length || 0}</div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-green-600" />
              Upcoming Exams
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700">{upcomingExams.length}</div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-amber-700 flex items-center">
              <ClockIcon className="h-4 w-4 mr-2 text-amber-600" />
              Past Exams
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-700">{completedExams.length}</div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 flex items-center">
              <CheckCircle2 className="h-4 w-4 mr-2 text-purple-600" />
              Pass Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700">-</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full md:w-auto grid-cols-3">
          <TabsTrigger value="all">All Exams</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="past">Past Exams</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          <ExamList exams={exams || []} />
        </TabsContent>
        
        <TabsContent value="upcoming" className="space-y-4">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-green-600" />
              Upcoming Exams
            </h3>
            {upcomingExams.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-gray-50">
                <p className="text-muted-foreground">No upcoming exams scheduled</p>
              </div>
            ) : (
              <ExamList exams={upcomingExams} />
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="past" className="space-y-4">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <ClockIcon className="h-5 w-5 mr-2 text-amber-600" />
              Past Exams
            </h3>
            {completedExams.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-gray-50">
                <p className="text-muted-foreground">No past exams found</p>
              </div>
            ) : (
              <ExamList exams={completedExams} />
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ExamDashboard;
