import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Loader2, Search } from 'lucide-react';
import { toast } from 'sonner';
import { searchStudents } from '@/api/students';
import { addExamResult } from '@/api/exams'; 
import type { Student } from '@/integrations/firebase/models/student';

interface AddExamResultProps {
  examId: string;
}

export const AddExamResult = ({ examId }: AddExamResultProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [marks, setMarks] = useState('');
  const [grade, setGrade] = useState('');
  const [remarks, setRemarks] = useState('');
  const queryClient = useQueryClient();

  const { data: students, isLoading } = useQuery({
    queryKey: ['students-search', searchQuery],
    queryFn: async () => {
      return await searchStudents(searchQuery);
    },
    enabled: searchQuery.length >= 2
  });

  const addResultMutation = useMutation({
    mutationFn: async (data: {
      student_id: string;
      marks: number;
      grade: string;
      remarks: string;
    }) => {
      return await addExamResult(
        examId,
        data.student_id,
        data.marks,
        data.grade,
        data.remarks
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exam-results'] });
      toast.success('Exam result added successfully');
      // Reset form
      setSelectedStudent(null);
      setMarks('');
      setGrade('');
      setRemarks('');
    },
    onError: (error: any) => {
      console.error('Error adding exam result:', error);
      toast.error(error.message || 'Failed to add exam result');
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedStudent) {
      toast.error('Please select a student');
      return;
    }

    const numericMarks = parseFloat(marks);
    if (isNaN(numericMarks)) {
      toast.error('Please enter valid marks');
      return;
    }

    addResultMutation.mutate({
      student_id: selectedStudent.id,
      marks: numericMarks,
      grade,
      remarks
    });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Search Student</Label>
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name or student ID"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
      )}

      {students && students.length > 0 && !selectedStudent && (
        <div className="border rounded-md divide-y">
          {students.map((student) => (
            <div
              key={student.id}
              className="p-2 hover:bg-muted cursor-pointer"
              onClick={() => setSelectedStudent(student)}
            >
              <div className="font-medium">{student.name}</div>
              <div className="text-sm text-muted-foreground">
                ID: {student.student_id}
              </div>
            </div>
          ))}
        </div>
      )}

      {students && students.length === 0 && searchQuery.length >= 2 && (
        <div className="text-center py-4 text-muted-foreground">
          No students found matching "{searchQuery}"
        </div>
      )}

      {selectedStudent && (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="p-4 border rounded-md bg-muted">
            <div className="font-medium">{selectedStudent.name}</div>
            <div className="text-sm text-muted-foreground">
              ID: {selectedStudent.student_id}
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setSelectedStudent(null)}
            >
              Change Student
            </Button>
          </div>

          <div className="space-y-2">
            <Label htmlFor="marks">Marks</Label>
            <Input
              id="marks"
              type="number"
              step="0.01"
              value={marks}
              onChange={(e) => setMarks(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="grade">Grade</Label>
            <Input
              id="grade"
              value={grade}
              onChange={(e) => setGrade(e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks</Label>
            <Input
              id="remarks"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={addResultMutation.isPending}
          >
            {addResultMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Result'
            )}
          </Button>
        </form>
      )}
    </div>
  );
};
