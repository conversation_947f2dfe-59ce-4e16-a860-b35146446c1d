import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON>, Eye, Printer, Pencil, Trash } from "lucide-react";
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Transaction, TransactionType } from "@/types/finance";
import { Student } from "@/types/student";
import { getStudentById, getStudentByStudentId } from "@/integrations/firebase/models/student";
import { collection, query, where, getDocs, getDoc, doc, updateDoc, deleteDoc } from 'firebase/firestore';
import { db } from '@/integrations/firebase/client';
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { handleError } from '@/utils/error-handling';
import type { Course } from "@/integrations/firebase/models/course";
import type { Level } from "@/integrations/firebase/models/level";

interface TransactionListProps {
  transactions: Transaction[];
  isLoading: boolean;
  searchQuery: string;
  transactionType: string;
  courseFilter: string;
  levelFilter: string;
  courses: Course[];
  filteredLevels: Level[];
  onSearchChange: (value: string) => void;
  onTypeChange: (value: string) => void;
  onCourseChange: (value: string) => void;
  onLevelChange: (value: string) => void;
}

export const TransactionList = ({
  transactions,
  isLoading,
  searchQuery,
  transactionType,
  courseFilter,
  levelFilter,
  courses,
  filteredLevels,
  onSearchChange,
  onTypeChange,
  onCourseChange,
  onLevelChange,
}: TransactionListProps) => {
  // For cache invalidation
  const queryClient = useQueryClient();
  
  // Add state for managing the dialog and selected transaction
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [studentData, setStudentData] = useState<Student | null>(null);
  const [isLoadingStudent, setIsLoadingStudent] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [transactionToEdit, setTransactionToEdit] = useState<Transaction | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [transactionToDelete, setTransactionToDelete] = useState<Transaction | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [editFormData, setEditFormData] = useState({
    type: '' as TransactionType,
    category: '',
    description: '',
    amount: 0
  });
  const [isSaving, setIsSaving] = useState(false);
  
  // Local state to manage transactions for immediate UI updates
  const [localTransactions, setLocalTransactions] = useState<Transaction[]>([]);
  
  // Sync local state with props
  useEffect(() => {
    setLocalTransactions(transactions);
  }, [transactions]);
  
  // Get user authentication context
  const { userProfile } = useAuth();
  
  // Check if user is super admin
  const isSuperAdmin = userProfile?.role === 'super_admin';
  
  console.log("User Profile in TransactionList:", userProfile);
  console.log("User Role:", userProfile?.role);

  // Handle edit transaction click
  const handleEditTransaction = (transaction: Transaction) => {
    console.log("Edit transaction button clicked for:", transaction.id);
    setTransactionToEdit(transaction);
    setEditFormData({
      type: transaction.type,
      category: String(transaction.category),
      description: transaction.description || '',
      amount: Number(transaction.amount)
    });
    setIsEditDialogOpen(true);
  };
  
  // Handle delete transaction click
  const handleDeleteTransaction = (transaction: Transaction) => {
    setTransactionToDelete(transaction);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle confirming transaction deletion
  const handleConfirmDelete = async () => {
    if (!transactionToDelete) return;
    
    try {
      setIsDeleting(true);
      
      // Delete the transaction from Firestore
      const transactionRef = doc(db, 'transactions', transactionToDelete.id);
      await deleteDoc(transactionRef);
      
      toast.success("Transaction deleted successfully");
      
      // Update local state
      setLocalTransactions(prevTransactions => 
        prevTransactions.filter(t => t.id !== transactionToDelete.id)
      );
      
      // Close the dialog
      setIsDeleteDialogOpen(false);
      
      // Invalidate queries to refresh data on the next fetch
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
    } catch (error) {
      console.error("Error deleting transaction:", error);
      toast.error(handleError(error, "Failed to delete transaction"));
    } finally {
      setIsDeleting(false);
    }
  };
  
  // Handle form data changes
  const handleEditFormChange = (field: string, value: any) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Handle save transaction changes
  const handleSaveTransaction = async () => {
    if (!transactionToEdit) return;
    
    try {
      setIsSaving(true);
      
      // Basic validation
      if (!editFormData.type) {
        toast.error("Type must be selected");
        return;
      }
      
      if (!editFormData.category.trim()) {
        toast.error("Category cannot be empty");
        return;
      }
      
      if (!editFormData.description.trim()) {
        toast.error("Description cannot be empty");
        return;
      }
      
      if (editFormData.amount <= 0) {
        toast.error("Amount must be greater than zero");
        return;
      }
      
      // Create update object with only changed fields
      const updateData: Partial<Transaction> = {};
      
      if (editFormData.type !== transactionToEdit.type) {
        updateData.type = editFormData.type;
      }
      
      if (editFormData.category !== String(transactionToEdit.category)) {
        updateData.category = editFormData.category;
      }
      
      if (editFormData.description !== transactionToEdit.description) {
        updateData.description = editFormData.description;
      }
      
      if (editFormData.amount !== Number(transactionToEdit.amount)) {
        updateData.amount = editFormData.amount;
      }
      
      // Only update if there are changes
      if (Object.keys(updateData).length > 0) {
        // Use direct Firestore update instead of the model function to avoid type mismatches
        const transactionRef = doc(db, 'transactions', transactionToEdit.id);
        await updateDoc(transactionRef, updateData as any);
        
        toast.success("Transaction updated successfully");
        
        // Update local state for immediate UI update
        setLocalTransactions(prevTransactions => 
          prevTransactions.map(transaction => 
            transaction.id === transactionToEdit.id 
              ? { ...transaction, ...updateData } 
              : transaction
          )
        );
        
        // Close the dialog
        setIsEditDialogOpen(false);
        
        // Invalidate queries to refresh data on the next fetch
        queryClient.invalidateQueries({ queryKey: ['transactions'] });
      } else {
        toast.info("No changes were made");
      }
    } catch (error) {
      console.error("Error updating transaction:", error);
      toast.error(handleError(error, "Failed to update transaction"));
    } finally {
      setIsSaving(false);
    }
  };

  // Fetch student data when a transaction is selected
  useEffect(() => {
    const fetchStudentData = async () => {
      if (selectedTransaction?.student_id) {
        setIsLoadingStudent(true);
        try {
          // First, try to get student by document ID
          let student = await getStudentById(selectedTransaction.student_id);
          
          // If transaction description contains student ID in format "ID: XXX",
          // try to extract and use it as a fallback
          if (!student && selectedTransaction.description) {
            // Try to extract student_id from the description
            // The format is typically "Fee payment from student [Name] (ID: [student_id])"
            const match = selectedTransaction.description.match(/\(ID:\s*([^)]+)\)/);
            if (match && match[1]) {
              const extractedStudentId = match[1].trim();
              console.log('Extracted student ID from description:', extractedStudentId);
              // Try to find student by their student_id field
              student = await getStudentByStudentId(extractedStudentId);
            }
          }
          
          if (student) {
            // Convert any Timestamp fields to string to ensure type compatibility
            const studentWithStringDates: Student = {
              ...student,
              created_at: typeof student.created_at === 'string' 
                ? student.created_at 
                : new Date().toISOString(), // Default to current date if timestamp is invalid
              updated_at: typeof student.updated_at === 'string'
                ? student.updated_at
                : new Date().toISOString() // Default to current date if timestamp is invalid
            };
            
            // Get course info if available
            if (student.course_id) {
              try {
                const courseDoc = await getDoc(doc(db, 'courses', student.course_id));
                if (courseDoc.exists()) {
                  const courseData = courseDoc.data();
                  studentWithStringDates.course = {
                    id: courseDoc.id,
                    name: courseData.name || '',
                    code: courseData.code || '',
                    description: courseData.description || null
                  };
                }
              } catch (error) {
                console.error("Error fetching course data:", error);
              }
            }
            
            // Get level info if available
            if (student.level_id) {
              try {
                const levelDoc = await getDoc(doc(db, 'levels', student.level_id));
                if (levelDoc.exists()) {
                  const levelData = levelDoc.data();
                  studentWithStringDates.level = {
                    id: levelDoc.id,
                    name: levelData.name || '',
                    code: levelData.code || null,
                    description: levelData.description || null
                  };
                }
              } catch (error) {
                console.error("Error fetching level data:", error);
              }
            }
            
            setStudentData(studentWithStringDates);
            
            // Now check for fee payment status
            await checkFeePaymentStatus(studentWithStringDates.id, selectedTransaction);
          } else {
            setStudentData(null);
            setPaymentStatus(null);
          }
        } catch (error) {
          console.error("Error fetching student data:", error);
          setStudentData(null);
          setPaymentStatus(null);
        } finally {
          setIsLoadingStudent(false);
        }
      } else {
        setStudentData(null);
        setPaymentStatus(null);
      }
    };
    
    fetchStudentData();
  }, [selectedTransaction]);

  // Check fee payment status (partial or complete)
  const checkFeePaymentStatus = async (studentId: string, transaction: Transaction) => {
    // Reset status
    setPaymentStatus(null);
    
    // Only proceed if it's a tuition payment
    if (!(transaction.category === 'tuition' || 
        (typeof transaction.category === 'string' && 
         transaction.category.toLowerCase().includes('tuition')))) {
      return;
    }
    
    try {
      // Get all tuition transactions for this student
      const transactionsQuery = query(
        collection(db, 'transactions'),
        where('student_id', '==', studentId),
        where('type', '==', 'income')
      );
      
      const transactionsSnapshot = await getDocs(transactionsQuery);
      const tuitionTransactions = transactionsSnapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() } as Transaction))
        .filter(t => t.category === 'tuition' || 
          (typeof t.category === 'string' && t.category.toLowerCase().includes('tuition')));

      // Calculate total paid
      const totalPaid = tuitionTransactions.reduce((sum, t) => sum + (Number(t.amount) || 0), 0);
      
      // Get fee amount from student data (if available)
      // This is a placeholder - you may need to adjust based on your data structure
      const feeQuery = query(
        collection(db, 'fees'),
        where('student_id', '==', studentId)
      );
      
      const feeSnapshot = await getDocs(feeQuery);
      let totalFees = 0;
      
      if (!feeSnapshot.empty) {
        const feeData = feeSnapshot.docs[0].data();
        totalFees = Number(feeData.amount) || 0;
      } else {
        // If no fee record, check if there's a default fee in the student's course
        if (studentData?.course_id) {
          const courseQuery = query(
            collection(db, 'courses'),
            where('id', '==', studentData.course_id)
          );
          
          const courseSnapshot = await getDocs(courseQuery);
          if (!courseSnapshot.empty) {
            const courseData = courseSnapshot.docs[0].data();
            totalFees = Number(courseData.fee) || 0;
          }
        }
      }
      
      // Determine payment status
      if (totalFees > 0) {
        if (totalPaid >= totalFees) {
          setPaymentStatus('completed');
        } else if (totalPaid > 0) {
          setPaymentStatus('partial');
        } else {
          setPaymentStatus('pending');
        }
      } else {
        // If we couldn't determine the total fee, use the transaction status
        setPaymentStatus(transaction.status);
      }
    } catch (error) {
      console.error("Error checking fee payment status:", error);
      // Fall back to the original status
      setPaymentStatus(transaction.status);
    }
  };

  const handleViewTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setPaymentStatus(null); // Reset payment status
    setIsDialogOpen(true);
  };
  
  // Get display status for a transaction
  const getDisplayStatus = (transaction: Transaction) => {
    // For a selected transaction in the dialog, use the calculated payment status if available
    if (selectedTransaction && transaction.id === selectedTransaction.id && paymentStatus) {
      return paymentStatus;
    }
    
    // For transactions in the list view, or if payment status isn't calculated yet
    if (transaction.category === 'tuition' || 
        (typeof transaction.category === 'string' && 
         transaction.category.toLowerCase().includes('tuition'))) {
      if (transaction.status === 'pending') {
        // Default to partial for pending tuition payments until calculated
        return 'partial';
      }
    }
    
    return transaction.status;
  };
  
  // Get status color based on status
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Simplified print function
  const onPrintReceipt = () => {
    if (selectedTransaction && studentData) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Payment Receipt</title>
              <style>
                body { font-family: Arial, sans-serif; }
                .receipt { padding: 20px; max-width: 800px; margin: 0 auto; }
                .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
                .logo-section { display: flex; align-items: center; }
                .logo { width: 60px; height: 60px; margin-right: 15px; }
                .school-name { font-size: 24px; font-weight: bold; }
                .receipt-details { text-align: right; }
                .student-info { margin-bottom: 20px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; padding: 15px 0; }
                .info-grid { display: flex; justify-content: space-between; }
                .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .table th, .table td { padding: 10px; text-align: left; }
                .table th { border-bottom: 1px solid #ddd; }
                .amount { text-align: right; font-weight: bold; }
                .total-row { border-top: 1px solid #ddd; }
                .footer { text-align: center; margin-top: 30px; color: #666; }
                .signatures { display: flex; justify-content: space-between; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd; }
              </style>
            </head>
            <body>
              <div class="receipt">
                <div class="header">
                  <div class="logo-section">
                    <img src="/logo.svg" alt="School Logo" class="logo" />
                    <div>
                      <div class="school-name">PTECH</div>
                      <div>Official Payment Receipt</div>
                    </div>
                  </div>
                  <div class="receipt-details">
                    <div>Receipt No: ${selectedTransaction.id.slice(-8).toUpperCase()}</div>
                    <div>Date: ${new Date().toLocaleDateString()}</div>
                  </div>
                </div>
                
                <div class="student-info">
                  <div class="info-grid">
                    <div>
                      <h3>Student Information</h3>
                      <div><strong>${studentData?.name || 'Student Not Found'}</strong></div>
                      <div>Student ID: ${studentData?.student_id || 'N/A'}</div>
                      ${studentData?.course?.name ? `<div>Course: ${studentData.course.name}</div>` : ''}
                      ${studentData?.level?.name ? `<div>Level: ${studentData.level.name}</div>` : ''}
                    </div>
                    <div>
                      <h3>Payment Details</h3>
                      <div>Date: ${new Date(selectedTransaction.date).toLocaleDateString()}</div>
                      <div>Transaction ID: ${selectedTransaction.id.slice(-8).toUpperCase()}</div>
                      <div>Payment Status: ${paymentStatus || getDisplayStatus(selectedTransaction)}</div>
                    </div>
                  </div>
                </div>
                
                <table class="table">
                  <thead>
                    <tr>
                      <th>Description</th>
                      <th>Category</th>
                      <th class="amount">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>${selectedTransaction.description}</td>
                      <td>${selectedTransaction.category.toString()}</td>
                      <td class="amount">Le ${Number(selectedTransaction.amount).toLocaleString()}</td>
                    </tr>
                    <tr class="total-row">
                      <td colspan="2" class="amount">Total:</td>
                      <td class="amount">Le ${Number(selectedTransaction.amount).toLocaleString()}</td>
                    </tr>
                  </tbody>
                </table>
                
                <div class="footer">
                  <p>Thank you for your payment.</p>
                  <p>This is an official receipt from PTECH.</p>
                </div>
                
                <div class="signatures">
                  <div>Issued by: ___________________</div>
                  <div>Received by: ___________________</div>
                </div>
              </div>
              <script>
                // Wait for images to load before printing
                window.onload = function() {
                  setTimeout(function() {
                    window.print();
                    window.close();
                  }, 300);
                };
              </script>
            </body>
          </html>
        `);
        printWindow.document.close();
      }
    }
  };

  // Use the local state for filtering instead of the prop
  const displayTransactions = localTransactions.filter((transaction) => {
    const matchesSearch = searchQuery === "" || 
      transaction.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (typeof transaction.category === 'string' && transaction.category?.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesType = transactionType === "all" || 
      transaction.type?.toLowerCase() === transactionType.toLowerCase();

    // Check if transaction was filtered by course/level filters
    const passesAdditionalFilters = !(transaction as any)._filtered;

    return matchesSearch && matchesType && passesAdditionalFilters;
  });

  // Use effect to asynchronously filter by course and level since we need to fetch student data
  useEffect(() => {
    if ((courseFilter === "all" && levelFilter === "all") || isLoading) {
      // Reset the filtered status when no course/level filters are applied
      setLocalTransactions(prevTransactions => 
        prevTransactions.map(transaction => ({
          ...transaction,
          _filtered: false
        }))
      );
      return;
    }

    // Only filter transactions that have student_id
    const transactionsToCheck = localTransactions.filter(t => t.student_id && 
      (matchesSearch(t, searchQuery) && matchesType(t, transactionType)));

    if (transactionsToCheck.length === 0) {
      return;
    }

    // Create a map to cache student data to avoid duplicate fetches
    const studentCache = new Map();

    // Create a Set of student IDs to efficiently check
    const uniqueStudentIds = new Set(transactionsToCheck.map(t => t.student_id));
    const filteredIds: string[] = [];

    const fetchStudentDataForFiltering = async () => {
      for (const studentId of uniqueStudentIds) {
        if (!studentId) continue;
        
        try {
          // Check if we've already fetched this student
          if (!studentCache.has(studentId)) {
            const student = await getStudentById(studentId);
            if (student) {
              studentCache.set(studentId, student);
            } else {
              continue; // Skip if student not found
            }
          }
          
          const student = studentCache.get(studentId);

          // Check if student has matching course
          if (courseFilter !== "all" && student.course_id !== courseFilter) {
            continue;
          }

          // Check if student has matching level
          if (levelFilter !== "all" && student.level_id !== levelFilter) {
            continue;
          }

          // If we get here, the student matches all criteria
          filteredIds.push(studentId);
        } catch (error) {
          console.error(`Error fetching student data for ${studentId}:`, error);
        }
      }

      // Update filtered transactions based on student IDs that passed filters
      setLocalTransactions(prevTransactions => 
        prevTransactions.map(transaction => {
          // Only filter transactions with student_id
          if (!transaction.student_id) {
            return { ...transaction, _filtered: false }; // No student ID, always show
          }
          
          // Hide transactions whose student_id didn't pass filters
          const isVisible = filteredIds.includes(transaction.student_id);
          
          return {
            ...transaction,
            _filtered: !isVisible // Hidden if not visible
          };
        })
      );
    };

    fetchStudentDataForFiltering();
  }, [courseFilter, levelFilter]);

  // Helper function to check if transaction matches search
  const matchesSearch = (transaction: Transaction, search: string) => {
    return search === "" || 
      transaction.description?.toLowerCase().includes(search.toLowerCase()) ||
      (typeof transaction.category === 'string' && 
        transaction.category?.toLowerCase().includes(search.toLowerCase()));
  };

  // Helper function to check if transaction matches type
  const matchesType = (transaction: Transaction, type: string) => {
    return type === "all" || transaction.type?.toLowerCase() === type.toLowerCase();
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-2 md:items-center mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search transactions..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
              />
            </div>
            <div className="flex flex-1 gap-2">
              <Select value={transactionType} onValueChange={onTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="All Transactions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Transactions</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                </SelectContent>
              </Select>

              <Select value={courseFilter} onValueChange={onCourseChange}>
                <SelectTrigger>
                  <SelectValue placeholder="All Courses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Courses</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select 
                value={levelFilter} 
                onValueChange={onLevelChange}
                disabled={courseFilter === "all"}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  {filteredLevels.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-700" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayTransactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center text-muted-foreground">
                      No transactions found
                    </TableCell>
                  </TableRow>
                ) : (
                  displayTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{new Date(transaction.date).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-sm ${
                          transaction.type?.toLowerCase() === 'income' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {transaction.type}
                        </span>
                      </TableCell>
                      <TableCell>{transaction.category}</TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell className={`text-right ${
                        transaction.type?.toLowerCase() === 'income' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type?.toLowerCase() === 'income' ? '+' : '-'}Le {Number(transaction.amount).toLocaleString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewTransaction(transaction)}
                            title="View transaction details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          {/* Show print receipt button only for tuition transactions */}
                          {(transaction.category === 'tuition' || 
                             (typeof transaction.category === 'string' && 
                              transaction.category.toLowerCase().includes('tuition'))) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedTransaction(transaction);
                                // Small delay to ensure the transaction is selected before printing
                                setTimeout(() => {
                                  onPrintReceipt();
                                }, 100);
                              }}
                              title="Print receipt"
                            >
                              <Printer className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {/* Show edit and delete buttons only for super admins */}
                          {isSuperAdmin && (
                            <>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditTransaction(transaction)}
                                title="Edit transaction"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteTransaction(transaction)}
                                title="Delete transaction"
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the transaction and remove it from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Transaction Dialog - Only for super admins */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Transaction</DialogTitle>
            <DialogDescription>
              Only super administrators can modify financial records. Make changes to this transaction below.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {transactionToEdit && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Transaction ID</h4>
                    <Input value={transactionToEdit.id} disabled />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-1">Date</h4>
                    <Input 
                      type="date" 
                      value={new Date(transactionToEdit.date).toISOString().split('T')[0]} 
                      disabled 
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium mb-1">Type</h4>
                  <Select 
                    value={editFormData.type} 
                    onValueChange={(value) => handleEditFormChange('type', value as TransactionType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select transaction type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="income">Income</SelectItem>
                      <SelectItem value="expense">Expense</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium mb-1">Category</h4>
                  <Input 
                    value={editFormData.category}
                    onChange={(e) => handleEditFormChange('category', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium mb-1">Description</h4>
                  <Input 
                    value={editFormData.description}
                    onChange={(e) => handleEditFormChange('description', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium mb-1">Amount (Le)</h4>
                  <Input 
                    type="number" 
                    value={editFormData.amount}
                    onChange={(e) => handleEditFormChange('amount', parseFloat(e.target.value))}
                  />
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveTransaction} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Transaction Detail Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Transaction Details</DialogTitle>
          </DialogHeader>
          
          {selectedTransaction && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Type</h4>
                  <p className={`inline-flex px-2 py-1 rounded-full text-sm ${
                    selectedTransaction.type?.toLowerCase() === 'income' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {selectedTransaction.type}
                  </p>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Amount</h4>
                  <p className={`font-semibold ${
                    selectedTransaction.type?.toLowerCase() === 'income' 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }`}>
                    Le {Number(selectedTransaction.amount).toLocaleString()}
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Date</h4>
                <p>{new Date(selectedTransaction.date).toLocaleDateString()}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Description</h4>
                <p>{selectedTransaction.description}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Category</h4>
                <p>{selectedTransaction.category}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Status</h4>
                <p className={`inline-flex px-2 py-1 rounded-full text-sm ${getStatusColor(getDisplayStatus(selectedTransaction))}`}>
                  {paymentStatus || getDisplayStatus(selectedTransaction)}
                </p>
              </div>
              
              {selectedTransaction.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Notes</h4>
                  <p>{selectedTransaction.notes}</p>
                </div>
              )}
              
              {selectedTransaction.payment_id && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Payment ID</h4>
                  <p>{selectedTransaction.payment_id}</p>
                </div>
              )}
              
              {selectedTransaction.student_id && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Student</h4>
                  {isLoadingStudent ? (
                    <div className="animate-pulse h-4 w-24 bg-gray-200 rounded"></div>
                  ) : (
                    <>
                      {studentData ? (
                        <>
                          <p className="font-medium">{studentData.name}</p>
                          <p className="text-sm">Student ID: {studentData.student_id}</p>
                          
                          {/* Show course and level info */}
                          {studentData.course && (
                            <p className="text-sm">Course: {studentData.course.name}</p>
                          )}
                          
                          {studentData.level && (
                            <p className="text-sm">Level: {studentData.level.name}</p>
                          )}
                          
                          {studentData.id && (
                            <a 
                              href={`/students/${studentData.id}`} 
                              className="text-xs text-blue-600 hover:underline mt-1 block"
                            >
                              View Student Profile
                            </a>
                          )}
                        </>
                      ) : (
                        <p>Student with document ID: {selectedTransaction.student_id}</p>
                      )}
                    </>
                  )}
                </div>
              )}
              
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>Close</Button>
            
            {/* Print receipt button (only for tuition) */}
            {selectedTransaction && 
              (selectedTransaction.category === 'tuition' || 
              (typeof selectedTransaction.category === 'string' && 
               selectedTransaction.category.toLowerCase().includes('tuition'))) && (
              <Button 
                onClick={onPrintReceipt}
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Receipt
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
