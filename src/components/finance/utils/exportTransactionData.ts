import { toast } from 'sonner';
import { Timestamp } from 'firebase/firestore';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

// Define a more flexible Transaction type for the export utility
interface ExportTransaction {
  id: string;
  date: string | Date | Timestamp;
  description: string;
  category: string;
  type: string;
  amount: number;
  status: string;
  notes?: string;
  created_at?: string | Date | Timestamp;
  updated_at?: string | Date | Timestamp;
  payment_id?: string;
  student_id?: string;
  [key: string]: any; // Allow additional properties
}

interface TransactionExportData {
  transactions: ExportTransaction[];
  title?: string;
}

/**
 * Helper function to convert Firebase timestamp or Date object to date string
 */
const formatDate = (date: any): string => {
  if (!date) return '';
  
  // Handle Firebase Timestamp
  if (date && typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {
    return date.toDate().toLocaleDateString();
  }
  
  // Handle JavaScript Date
  if (date instanceof Date) {
    return date.toLocaleDateString();
  }
  
  // Handle ISO string date
  if (typeof date === 'string') {
    try {
      return new Date(date).toLocaleDateString();
    } catch (e) {
      return date;
    }
  }
  
  return String(date);
};

/**
 * Exports transaction data to PDF file
 * For fee/tuition transactions, includes student information
 */
export const exportTransactionsToPDF = async ({
  transactions,
  title = 'Transactions'
}: TransactionExportData) => {
  let loadingToastId: string | number | undefined;
  
  try {
    // Show loading toast
    loadingToastId = toast.loading('Preparing transaction data for PDF export...');
    
    // Create new PDF document - A4 paper, portrait
    const pdf = new jsPDF();
    
    // Set title colors
    const headerColor = [34, 100, 34]; // Dark green
    const subheaderColor = [80, 120, 80]; // Lighter green
    
    // Add title to the PDF
    pdf.setFontSize(18);
    pdf.setTextColor(headerColor[0], headerColor[1], headerColor[2]);
    pdf.text(title, 14, 22);
    
    // Add timestamp
    pdf.setFontSize(10);
    pdf.setTextColor(subheaderColor[0], subheaderColor[1], subheaderColor[2]);
    const now = new Date();
    pdf.text(`Generated on: ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`, 14, 28);
    
    // Add transaction count
    pdf.text(`Total Transactions: ${transactions.length}`, 14, 33);
    
    // Add summary section
    await addSummarySection(pdf, transactions);
    
    // Add transactions section
    await addTransactionsSection(pdf, transactions);
    
    // If there are fee/tuition transactions, add student details section
    const feeTransactions = transactions.filter(t => 
      (typeof t.category === 'string' && t.category.toLowerCase().includes('tuition')) || 
      t.category === 'tuition'
    );
    
    if (feeTransactions.length > 0) {
      await addStudentFeeSection(pdf, feeTransactions);
    }
    
    // Generate file name
    const date = new Date().toISOString().split('T')[0];
    const fileName = `${title.replace(/\s+/g, '_')}_${date}.pdf`;
    
    // Save PDF
    pdf.save(fileName);
    
    // Success message
    if (loadingToastId) {
      toast.dismiss(loadingToastId);
      toast.success('Transactions exported successfully as PDF');
    }
    
    return true;
  } catch (error) {
    console.error('Export error:', error);
    if (loadingToastId) {
      toast.dismiss(loadingToastId);
      toast.error('Failed to export transaction data to PDF');
    }
    return false;
  }
};

/**
 * Add summary section to the PDF
 */
const addSummarySection = async (pdf: jsPDF, transactions: ExportTransaction[]): Promise<void> => {
  // Calculate totals
  const income = transactions
    .filter(t => t.type?.toLowerCase() === 'income')
    .reduce((total, t) => total + Number(t.amount), 0);
  
  const expense = transactions
    .filter(t => t.type?.toLowerCase() === 'expense')
    .reduce((total, t) => total + Number(t.amount), 0);
  
  const netAmount = income - expense;
  
  // Count by category
  const categories = new Set<string>();
  transactions.forEach(t => categories.add(String(t.category)));
  
  const categoryTotals: Record<string, { count: number, amount: number }> = {};
  
  transactions.forEach(t => {
    const category = String(t.category);
    if (!categoryTotals[category]) {
      categoryTotals[category] = { count: 0, amount: 0 };
    }
    categoryTotals[category].count += 1;
    categoryTotals[category].amount += Number(t.amount);
  });
  
  // Add summary header
  pdf.setFontSize(14);
  pdf.setTextColor(0, 0, 0);
  pdf.text('Financial Summary (Filtered Data)', 14, 45);
  
  // Create summary table
  const summaryData = [
    ['Total Filtered Transactions', transactions.length.toString()],
    ['Total Income', `Le ${income.toLocaleString()}`],
    ['Total Expenses', `Le ${expense.toLocaleString()}`],
    ['Net Amount', `Le ${netAmount.toLocaleString()}`]
  ];
  
  autoTable(pdf, {
    startY: 50,
    head: [['Item', 'Value']],
    body: summaryData,
    theme: 'grid',
    headStyles: { fillColor: [46, 125, 50], textColor: [255, 255, 255] },
    margin: { top: 30 }
  });
  
  // Add category breakdown
  let finalY = (pdf as any).lastAutoTable.finalY + 10;
  
  pdf.setFontSize(14);
  pdf.text('Category Breakdown', 14, finalY);
  finalY += 5;
  
  const categoryData = Object.entries(categoryTotals).map(([category, { count, amount }]) => [
    category,
    count.toString(),
    `Le ${amount.toLocaleString()}`
  ]);
  
  autoTable(pdf, {
    startY: finalY,
    head: [['Category', 'Count', 'Total Amount']],
    body: categoryData,
    theme: 'grid',
    headStyles: { fillColor: [46, 125, 50], textColor: [255, 255, 255] }
  });
};

/**
 * Add transactions section to the PDF
 */
const addTransactionsSection = async (pdf: jsPDF, transactions: ExportTransaction[]): Promise<void> => {
  // Add transactions header
  let finalY = (pdf as any).lastAutoTable.finalY + 15;
  
  pdf.setFontSize(14);
  pdf.text('Filtered Transaction Details', 14, finalY);
  finalY += 5;
  
  // Create transaction table data
  // Sort transactions by date (newest first)
  const sortedTransactions = [...transactions].sort((a, b) => {
    const dateA = a.date ? (typeof a.date === 'object' && 'toDate' in a.date) ? a.date.toDate().getTime() : new Date(a.date).getTime() : 0;
    const dateB = b.date ? (typeof b.date === 'object' && 'toDate' in b.date) ? b.date.toDate().getTime() : new Date(b.date).getTime() : 0;
    return dateB - dateA;
  });
  
  const transactionData = sortedTransactions.map(transaction => [
    formatDate(transaction.date),
    transaction.type,
    transaction.category,
    transaction.description,
    `Le ${Number(transaction.amount).toLocaleString()}`,
    transaction.status
  ]);
  
  autoTable(pdf, {
    startY: finalY,
    head: [['Date', 'Type', 'Category', 'Description', 'Amount', 'Status']],
    body: transactionData,
    theme: 'striped',
    headStyles: { fillColor: [46, 125, 50], textColor: [255, 255, 255] },
    styles: { overflow: 'linebreak' },
    columnStyles: {
      0: { cellWidth: 25 }, // Date
      1: { cellWidth: 20 }, // Type
      2: { cellWidth: 25 }, // Category
      3: { cellWidth: 'auto' }, // Description (auto width)
      4: { cellWidth: 25, halign: 'right' }, // Amount (right-aligned)
      5: { cellWidth: 25 } // Status
    }
  });
};

/**
 * Add student fee section to the PDF
 */
const addStudentFeeSection = async (pdf: jsPDF, feeTransactions: ExportTransaction[]): Promise<void> => {
  // Add page if needed
  let finalY = (pdf as any).lastAutoTable.finalY + 15;
  if (finalY > pdf.internal.pageSize.height - 40) {
    pdf.addPage();
    finalY = 20;
  }
  
  // Add student fees header
  pdf.setFontSize(14);
  pdf.text('Filtered Student Fee Transactions', 14, finalY);
  
  // Calculate total fee amount
  const totalFeeAmount = feeTransactions.reduce((total, t) => total + Number(t.amount), 0);
  
  // Add fee summary
  pdf.setFontSize(10);
  finalY += 5;
  pdf.text(`Total Students: ${new Set(feeTransactions.filter(t => t.student_id).map(t => t.student_id)).size}`, 14, finalY);
  finalY += 5;
  pdf.text(`Total Fee Amount: Le ${totalFeeAmount.toLocaleString()}`, 14, finalY);
  finalY += 8;
  
  // Import dynamically to avoid type conflicts
  const { getStudentById } = await import('@/integrations/firebase/models/student');
  const { getCourseById } = await import('@/integrations/firebase/models/course');
  const { getLevelById } = await import('@/integrations/firebase/models/level');
  
  // Get unique student IDs
  const studentIds = new Set<string>();
  feeTransactions.forEach(t => {
    if (t.student_id) studentIds.add(t.student_id);
  });
  
  // Track course statistics
  const courseStats: Record<string, { count: number, amount: number, name: string }> = {};
  
  // Cache for data to avoid duplicate fetches
  const studentDataCache: Record<string, any> = {};
  const courseDataCache: Record<string, any> = {};
  const levelDataCache: Record<string, any> = {};
  
  // Prepare data for the table
  const studentFeeData = [];
  
  // Fetch student data for each transaction
  for (const transaction of feeTransactions) {
    if (!transaction.student_id) {
      // If no student ID, add transaction with empty student fields
      studentFeeData.push([
        formatDate(transaction.date),
        `Le ${Number(transaction.amount).toLocaleString()}`,
        transaction.status,
        'Unknown',
        '',
        '',
        ''
      ]);
      continue;
    }
    
    // Get or fetch student data
    let student: any = null;
    let courseName = '';
    let levelName = '';
    let courseId = '';
    let studentIdDisplay = transaction.student_id.slice(-8).toUpperCase(); // Default to part of the document ID
    
    try {
      // Get student data from cache or fetch it
      if (studentDataCache[transaction.student_id]) {
        student = studentDataCache[transaction.student_id];
      } else {
        student = await getStudentById(transaction.student_id);
        if (student) {
          studentDataCache[transaction.student_id] = student;
        }
      }
      
      if (student) {
        // Use student_id field if available (the display ID, not document ID)
        if (student.student_id) {
          studentIdDisplay = student.student_id;
        }
        
        // Get course data if available
        if (student.course_id) {
          courseId = student.course_id;
          if (courseDataCache[student.course_id]) {
            courseName = courseDataCache[student.course_id].name;
          } else {
            const course = await getCourseById(student.course_id);
            if (course) {
              courseDataCache[student.course_id] = course;
              courseName = course.name;
            }
          }
        }
        
        // Get level data if available
        if (student.level_id) {
          if (levelDataCache[student.level_id]) {
            levelName = levelDataCache[student.level_id].name;
          } else {
            const level = await getLevelById(student.level_id);
            if (level) {
              levelDataCache[student.level_id] = level;
              levelName = level.name;
            }
          }
        }
        
        // If student already has course/level objects attached
        if (student.course && student.course.name) {
          courseName = student.course.name;
          courseId = student.course_id || student.course.id || '';
        }
        
        if (student.level && student.level.name) {
          levelName = student.level.name;
        }
        
        // Track course statistics
        if (courseId && courseName) {
          if (!courseStats[courseId]) {
            courseStats[courseId] = { count: 0, amount: 0, name: courseName };
          }
          courseStats[courseId].count++;
          courseStats[courseId].amount += Number(transaction.amount);
        }
      }
    } catch (error) {
      console.error(`Error fetching data for student ${transaction.student_id}:`, error);
    }
    
    // Add transaction with student data
    studentFeeData.push([
      formatDate(transaction.date),
      `Le ${Number(transaction.amount).toLocaleString()}`,
      transaction.status,
      student?.name || 'Unknown',
      studentIdDisplay,
      courseName,
      levelName
    ]);
  }
  
  // Add student fee table
  autoTable(pdf, {
    startY: finalY,
    head: [['Date', 'Amount', 'Status', 'Student Name', 'Student ID', 'Course', 'Level']],
    body: studentFeeData,
    theme: 'grid',
    headStyles: { fillColor: [46, 125, 50], textColor: [255, 255, 255] },
    styles: { overflow: 'linebreak' },
    columnStyles: {
      0: { cellWidth: 20 }, // Date
      1: { cellWidth: 20 }, // Amount
      2: { cellWidth: 20 }, // Status
      3: { cellWidth: 30 }, // Student Name
      4: { cellWidth: 20 }, // Student ID
      5: { cellWidth: 30 }, // Course
      6: { cellWidth: 30 } // Level
    }
  });
  
  // Add course breakdown if multiple courses exist
  const courseEntries = Object.entries(courseStats);
  if (courseEntries.length > 0) {
    let y = (pdf as any).lastAutoTable.finalY + 15;
    
    // Add new page if needed
    if (y > pdf.internal.pageSize.height - 60) {
      pdf.addPage();
      y = 20;
    }
    
    pdf.setFontSize(14);
    pdf.text('Course Fee Breakdown', 14, y);
    y += 10;
    
    const courseData = courseEntries.map(([id, { name, count, amount }]) => [
      name,
      count.toString(),
      `Le ${amount.toLocaleString()}`
    ]);
    
    autoTable(pdf, {
      startY: y,
      head: [['Course Name', 'Student Count', 'Total Amount']],
      body: courseData,
      theme: 'grid',
      headStyles: { fillColor: [46, 125, 50], textColor: [255, 255, 255] }
    });
  }
}; 