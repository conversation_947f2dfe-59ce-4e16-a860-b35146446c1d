import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, User, Mail, Phone, BookOpen, School, Calendar, Clock } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import { ProfileDocument } from '@/integrations/firebase/firestore';
import { update } from '@/integrations/firebase/firestore';
import { format, addDays, startOfWeek, endOfWeek } from 'date-fns';
import { getClassTimetableByTeacher } from '@/api/timetable';
import { getSubjects } from '@/api/subjects';
import type { ClassTimetableEntry } from '@/integrations/firebase/models/timetable';
import { useQuery } from '@tanstack/react-query';

// Extended profile type to include teacher-specific fields
interface TeacherProfileDocument extends ProfileDocument {
  bio?: string;
  contact_number?: string;
  specialization?: string;
  assigned_levels?: string[];
  assigned_courses?: string[];
}

const TeacherProfile = () => {
  const { userProfile, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Cast userProfile to TeacherProfileDocument
  const teacherProfile = userProfile as TeacherProfileDocument | null;

  const [formData, setFormData] = useState({
    displayName: teacherProfile?.displayName || '',
    email: teacherProfile?.email || '',
    bio: teacherProfile?.bio || '',
    contact_number: teacherProfile?.contact_number || '',
    specialization: teacherProfile?.specialization || '',
    photoURL: teacherProfile?.photoURL || ''
  });

  const handleUpdateProfile = async () => {
    if (!teacherProfile?.id) return;
    
    setIsLoading(true);
    try {
      // Implement actual profile update using Firestore
      await update<TeacherProfileDocument>('profiles', teacherProfile.id, {
        displayName: formData.displayName,
        email: formData.email,
        photoURL: formData.photoURL,
        role: teacherProfile.role,
        // Custom fields for teacher profile
        bio: formData.bio,
        contact_number: formData.contact_number,
        specialization: formData.specialization
      });
      
      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
      setIsEditing(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!teacherProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const getInitials = (name: string | undefined | null) => {
    if (!name) return 'NA'; // Return 'NA' (Not Available) if name is undefined or null
    
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Teacher Profile</h2>
          <p className="text-muted-foreground">
            View and manage your profile information
          </p>
        </div>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>
            Edit Profile
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateProfile} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        )}
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="courses">Courses</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
                <CardDescription>
                  Your basic profile details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-6">
                  <div className="flex justify-center">
                    <Avatar className="h-24 w-24">
                      <AvatarImage src={teacherProfile.photoURL} />
                      <AvatarFallback className="text-lg">
                        {getInitials(teacherProfile.displayName)}
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  {isEditing ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className="text-right">
                          Name
                        </Label>
                        <Input
                          id="name"
                          value={formData.displayName}
                          onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="email" className="text-right">
                          Email
                        </Label>
                        <Input
                          id="email"
                          value={formData.email}
                          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                          className="col-span-3"
                          disabled
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="contact" className="text-right">
                          Phone
                        </Label>
                        <Input
                          id="contact"
                          value={formData.contact_number}
                          onChange={(e) => setFormData(prev => ({ ...prev, contact_number: e.target.value }))}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="specialization" className="text-right">
                          Specialization
                        </Label>
                        <Input
                          id="specialization"
                          value={formData.specialization}
                          onChange={(e) => setFormData(prev => ({ ...prev, specialization: e.target.value }))}
                          className="col-span-3"
                        />
                      </div>
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="bio" className="text-right">
                          Bio
                        </Label>
                        <Textarea
                          id="bio"
                          value={formData.bio}
                          onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
                          className="col-span-3"
                          rows={4}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <User className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Full Name</p>
                          <p className="font-medium">{teacherProfile.displayName}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Mail className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Email</p>
                          <p className="font-medium">{teacherProfile.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Phone className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Contact Number</p>
                          <p className="font-medium">{teacherProfile.contact_number || 'Not provided'}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <BookOpen className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Specialization</p>
                          <p className="font-medium">{teacherProfile.specialization || 'Not specified'}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Teaching Information</CardTitle>
                <CardDescription>
                  Information about your teaching role
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Joined On</p>
                      <p className="font-medium">
                        {teacherProfile.created_at ? new Date(teacherProfile.created_at.seconds * 1000).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <School className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Assigned Levels</p>
                      <p className="font-medium">{teacherProfile.assigned_levels?.length || 0} levels</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <BookOpen className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Assigned Courses</p>
                      <p className="font-medium">{teacherProfile.assigned_courses?.length || 0} courses</p>
                    </div>
                  </div>

                  {!isEditing && (
                    <div className="pt-4">
                      <h4 className="font-medium mb-2">Biography</h4>
                      <p className="text-sm text-muted-foreground">
                        {teacherProfile.bio || 'No biography provided yet.'}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="courses">
          <Card>
            <CardHeader>
              <CardTitle>Assigned Courses</CardTitle>
              <CardDescription>
                Courses you are currently teaching
              </CardDescription>
            </CardHeader>
            <CardContent>
              {teacherProfile.assigned_courses?.length ? (
                <div className="space-y-4">
                  <p>Course list implementation</p>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No courses assigned yet
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule">
          <Card>
            <CardHeader>
              <CardTitle>Teaching Schedule</CardTitle>
              <CardDescription>
                Your weekly class schedule
              </CardDescription>
            </CardHeader>
            <CardContent>
              {(() => {
                // Fetch teacher's timetable
                const { data: timetableEntries = [], isLoading: isLoadingTimetable } = useQuery<ClassTimetableEntry[]>({
                  queryKey: ['timetable', teacherProfile?.id],
                  queryFn: () => teacherProfile?.id ? getClassTimetableByTeacher(teacherProfile.id) : Promise.resolve([]),
                  enabled: !!teacherProfile?.id
                });

                // Fetch subjects data
                const { data: subjects = [], isLoading: isLoadingSubjects } = useQuery({
                  queryKey: ['subjects'],
                  queryFn: getSubjects
                });

                // Helper function to get subject name
                const getSubjectName = (subjectId: string) => {
                  const subject = subjects.find(s => s.id === subjectId);
                  return subject ? `${subject.name} (${subject.code})` : subjectId;
                };

                // Helper function to format time
                const formatTime = (timeString: string) => {
                  try {
                    const [hours, minutes] = timeString.split(':').map(Number);
                    const date = new Date();
                    date.setHours(hours, minutes, 0, 0);
                    return date.toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    });
                  } catch (error) {
                    return timeString;
                  }
                };

                if (isLoadingTimetable || isLoadingSubjects) {
                  return (
                    <div className="flex items-center justify-center h-32">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    </div>
                  );
                }

                const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

                return (
                  <div className="space-y-6">
                    {weekDays.map((day) => {
                      const dayClasses = timetableEntries.filter(entry => entry.day === day)
                        .sort((a, b) => a.startTime.localeCompare(b.startTime));

                      return (
                        <div key={day} className="space-y-2">
                          <h3 className="font-medium">{day}</h3>
                          {dayClasses.length === 0 ? (
                            <div className="text-sm text-muted-foreground">
                              No classes scheduled
                            </div>
                          ) : (
                            <div className="grid gap-2">
                              {dayClasses.map((entry, index) => (
                                <Card key={index}>
                                  <CardContent className="p-4">
                                    <div className="flex items-center justify-between">
                                      <div>
                                        <h4 className="font-medium">{getSubjectName(entry.subject)}</h4>
                                        <div className="flex items-center text-sm text-muted-foreground mt-1">
                                          <Clock className="h-4 w-4 mr-1" />
                                          {formatTime(entry.startTime)} - {formatTime(entry.endTime)}
                                        </div>
                                      </div>
                                      <div className="text-right">
                                        <div className="text-sm">{entry.room}</div>
                                        <div className="text-sm text-muted-foreground mt-1">Room</div>
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TeacherProfile; 