import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Calendar, Clock, Users, MapPin, ChevronLeft, ChevronRight } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getLevels } from '@/api/levels';
import { format, addDays, startOfWeek, endOfWeek, isSameDay } from 'date-fns';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { getClassTimetableByTeacher } from '@/api/timetable';
import type { ClassTimetableEntry } from '@/integrations/firebase/models/timetable';
import { getSubjects } from '@/api/subjects';

// Extended profile type to include teacher-specific fields
interface TeacherProfileDocument {
  id?: string;
  email: string;
  displayName: string;
  role: string;
  photoURL: string;
  assigned_levels?: string[];
  assigned_courses?: string[];
  specialization?: string;
  bio?: string;
  contact_number?: string;
}

const TimetableSchedule = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedView, setSelectedView] = useState<'week' | 'day'>('week');
  const [subjects, setSubjects] = useState<any[]>([]);

  // Cast userProfile to TeacherProfileDocument
  const teacherProfile = userProfile as TeacherProfileDocument | null;

  // Fetch levels data
  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Fetch teacher's timetable
  const { data: timetableEntries = [], isLoading: isLoadingTimetable } = useQuery<ClassTimetableEntry[]>({
    queryKey: ['timetable', teacherProfile?.id],
    queryFn: () => teacherProfile?.id ? getClassTimetableByTeacher(teacherProfile.id) : Promise.resolve([]),
    enabled: !!teacherProfile?.id
  });

  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const subjectsData = await getSubjects();
        setSubjects(subjectsData);
      } catch (error) {
        console.error('Error fetching subjects:', error);
        toast({
          title: "Error",
          description: "Failed to load subjects data",
          variant: "destructive",
        });
      }
    };

    fetchSubjects();
  }, [toast]);

  // Calculate week range
  const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 });
  const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 0 });

  // Get days of the week
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  // Filter timetable for selected day
  const getTimetableForDay = (dayName: string) => {
    return timetableEntries.filter(entry => entry.day === dayName);
  };

  const handlePreviousWeek = () => {
    setSelectedDate(prev => addDays(prev, -7));
  };

  const handleNextWeek = () => {
    setSelectedDate(prev => addDays(prev, 7));
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      setSelectedView('day');
    }
  };

  const getSubjectName = (subjectId: string) => {
    const subject = subjects.find((s) => s.id === subjectId);
    return subject ? `${subject.name} (${subject.code})` : subjectId;
  };

  const getLevelName = (levelId: string) => {
    const level = levels.find(l => l.id === levelId);
    return level ? level.name : levelId;
  };

  const formatTimeString = (timeString: string) => {
    try {
      // Parse time string (assuming it's in format like "09:00" or "14:30")
      const [hours, minutes] = timeString.split(':').map(Number);
      
      // Create a date object with current date but with the time from timeString
      const date = new Date();
      date.setHours(hours, minutes, 0, 0);
      
      // Format to display time in 12-hour format with AM/PM
      return date.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit', 
        hour12: true 
      });
    } catch (error) {
      return timeString; // Return original string if parsing fails
    }
  };

  if (!teacherProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  const isLoading = isLoadingLevels || isLoadingTimetable;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold tracking-tight">Teaching Schedule</h1>
      <p className="text-muted-foreground">View your teaching timetable for classes.</p>

      <div className="flex justify-between items-center my-4">
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handlePreviousWeek}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="font-medium">
            {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
          </span>
          <Button variant="outline" onClick={handleNextWeek}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Select Date</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <Select value={selectedView} onValueChange={(value) => setSelectedView(value as 'week' | 'day')}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="View" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Week View</SelectItem>
              <SelectItem value="day">Day View</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {selectedView === 'week' ? (
        <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
          {weekDays.map((day, index) => {
            const dayName = format(day, 'EEEE');
            const dayClasses = getTimetableForDay(dayName);
            const isToday = isSameDay(day, new Date());
            
            return (
              <Card 
                key={index} 
                className={`${isToday ? 'border-primary' : ''}`}
              >
                <CardHeader className={`${isToday ? 'bg-primary/10' : ''} py-2`}>
                  <CardTitle className="text-center text-sm font-medium">
                    {format(day, 'EEEE')}
                  </CardTitle>
                  <CardDescription className="text-center text-xs">
                    {format(day, 'MMM d')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-2">
                  {dayClasses.length === 0 ? (
                    <div className="text-center py-6 text-sm text-muted-foreground">
                      No classes scheduled
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {dayClasses
                        .sort((a, b) => a.startTime.localeCompare(b.startTime))
                        .map((entry, classIndex) => (
                          <div 
                            key={classIndex} 
                            className="p-2 text-xs border rounded-md bg-muted/30"
                          >
                            <div className="font-medium mb-1">{getSubjectName(entry.subject)}</div>
                            <div className="flex items-center text-muted-foreground">
                              <Clock className="h-3 w-3 mr-1" />
                              <span>
                                {formatTimeString(entry.startTime)} - {formatTimeString(entry.endTime)}
                              </span>
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <Users className="h-3 w-3 mr-1" />
                              <span>{getLevelName(entry.level)}</span>
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <MapPin className="h-3 w-3 mr-1" />
                              <span>{entry.room}</span>
                            </div>
                          </div>
                        ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>{format(selectedDate, 'EEEE, MMMM d, yyyy')}</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const dayName = format(selectedDate, 'EEEE');
              const dayClasses = getTimetableForDay(dayName);
              
              if (dayClasses.length === 0) {
                return (
                  <div className="text-center py-6 text-muted-foreground">
                    No classes scheduled for {dayName}
                  </div>
                );
              }
              
              return (
                <div className="space-y-4">
                  {dayClasses
                    .sort((a, b) => a.startTime.localeCompare(b.startTime))
                    .map((entry, index) => (
                      <div 
                        key={index} 
                        className="p-4 border rounded-md bg-muted/30 flex flex-col md:flex-row md:items-center md:justify-between"
                      >
                        <div className="space-y-1 mb-2 md:mb-0">
                          <h3 className="font-medium">{getSubjectName(entry.subject)}</h3>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Clock className="h-4 w-4 mr-1" />
                            <span>
                              {formatTimeString(entry.startTime)} - {formatTimeString(entry.endTime)}
                            </span>
                          </div>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Users className="h-4 w-4 mr-1" />
                            <span>{getLevelName(entry.level)}</span>
                          </div>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <MapPin className="h-4 w-4 mr-1" />
                            <span>{entry.room}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              );
            })()}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TimetableSchedule; 