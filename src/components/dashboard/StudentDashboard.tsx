import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, Routes, Route, Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Student } from '@/types/student';
import { Timestamp, DocumentData } from 'firebase/firestore';
import { getById } from '@/integrations/firebase/firestore';
import ErrorBoundary from '@/components/ErrorBoundary';

// Import student dashboard components
import {
  StudentOverview,
  StudentProfile,
  CourseOverview,
  Timetable,
  Assignments,
  Exams,
  Attendance,
  CourseMaterials,
  Announcements,
  EventCalendar,
  Feedback,
  StudentLibrary,
  Grades,
  StudentDissertations
} from '@/components/student';

interface StudentDashboardProps {
  setSidebarOpen?: (open: boolean) => void;
}

interface StudentData extends DocumentData {
  id: string;
  created_at: Timestamp | { seconds: number; nanoseconds: number };
  updated_at: Timestamp | { seconds: number; nanoseconds: number };
  [key: string]: any;
}

const StudentDashboard = ({ setSidebarOpen }: StudentDashboardProps) => {
  const { user, userProfile } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [student, setStudent] = useState<Student | null>(null);
  const [isStudentLoading, setIsStudentLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Close sidebar on mobile when navigating to a new page
  useEffect(() => {
    if (setSidebarOpen && window.innerWidth < 1024) {
      setSidebarOpen(false);
    }
  }, [location.pathname, setSidebarOpen]);

  useEffect(() => {
    if (!user || !userProfile) {
      navigate('/login', { replace: true });
      return;
    }

    if (userProfile.role !== 'student') {
      navigate('/dashboard', { replace: true });
      return;
    }

    const fetchStudentData = async () => {
      // Check for studentId in different possible locations
      const studentId = userProfile.studentId || (userProfile as any).student_id || user.uid;
      
      if (!studentId) {
        console.error('No student ID found in user profile:', userProfile);
        setError('No student ID found in user profile');
        setIsStudentLoading(false);
        return;
      }

      try {
        const studentData = await getById('students', studentId) as StudentData;
        
        if (!studentData) {
          console.error('Student data not found for ID:', studentId);
          setError('Student data not found');
          setIsStudentLoading(false);
          return;
        }

        // Convert timestamp-like objects to Timestamp instances if needed
        const createdAt = studentData.created_at instanceof Timestamp 
          ? studentData.created_at
          : new Timestamp(
              (studentData.created_at as { seconds: number }).seconds,
              (studentData.created_at as { nanoseconds: number }).nanoseconds
            );
          
        const updatedAt = studentData.updated_at instanceof Timestamp
          ? studentData.updated_at
          : new Timestamp(
              (studentData.updated_at as { seconds: number }).seconds,
              (studentData.updated_at as { nanoseconds: number }).nanoseconds
            );

        // Normalize the student data structure
        const normalizedStudent = {
          ...studentData,
          created_at: createdAt,
          updated_at: updatedAt
        } as Student;

        // Fetch course details if we only have the ID
        if (studentData.course_id && !studentData.course) {
          try {
            const courseData = await getById('courses', studentData.course_id);
            if (courseData) {
              normalizedStudent.course = {
                id: studentData.course_id,
                name: courseData.name || 'Unknown Course',
                code: courseData.code || '',
                description: courseData.description || null
              };
            } else {
              // If course data can't be fetched, at least create a basic structure
              normalizedStudent.course = {
                id: studentData.course_id,
                name: studentData.course_name || 'Unknown Course',
                code: '',
                description: null
              };
            }
          } catch (error) {
            console.error('Error fetching course data:', error);
            // Create a basic structure with what we have
            normalizedStudent.course = {
              id: studentData.course_id,
              name: studentData.course_name || 'Unknown Course',
              code: '',
              description: null
            };
          }
        }

        // Fetch level details if we only have the ID
        if (studentData.level_id && !studentData.level) {
          try {
            const levelData = await getById('levels', studentData.level_id);
            if (levelData) {
              normalizedStudent.level = {
                id: studentData.level_id,
                name: levelData.name || 'Unknown Level',
                code: levelData.code || null,
                description: levelData.description || null
              };
            } else {
              // If level data can't be fetched, at least create a basic structure
              normalizedStudent.level = {
                id: studentData.level_id,
                name: studentData.level_name || 'Unknown Level',
                code: null,
                description: null
              };
            }
          } catch (error) {
            console.error('Error fetching level data:', error);
            // Create a basic structure with what we have
            normalizedStudent.level = {
              id: studentData.level_id,
              name: studentData.level_name || 'Unknown Level',
              code: null,
              description: null
            };
          }
        }

        setStudent(normalizedStudent);
        setError(null);
      } catch (error) {
        console.error('Error fetching student data:', error);
        setError('Failed to load student data. Please try again.');
      } finally {
        setIsStudentLoading(false);
      }
    };

    fetchStudentData();
  }, [user, userProfile, navigate]);

  // Handle loading state
  if (isStudentLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-green-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700" />
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-green-50 p-8">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error}</p>
          <Button 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Main Content */}
      <div className="w-full">
        <div className="px-4 py-6 sm:px-6 lg:px-8">
          {/* Navigation Breadcrumbs */}
          <div className="mb-6 flex items-center space-x-2 text-sm text-gray-500">
            <Link to="/dashboard/student" className="hover:text-green-600">Dashboard</Link>
            {location.pathname !== '/dashboard/student' && (
              <>
                <span>/</span>
                <span className="capitalize">
                  {location.pathname.split('/').pop()?.replace('-', ' ')}
                </span>
              </>
            )}
          </div>

          {/* Main Content Area */}
          <ErrorBoundary>
            <Routes>
              <Route index element={<StudentOverview student={student} />} />
              <Route path="profile" element={<StudentProfile student={student} />} />
              <Route path="courses" element={<CourseOverview student={student} />} />
              <Route path="timetable" element={<Timetable student={student} />} />
              <Route path="assignments/*" element={<Assignments student={student} />} />
              <Route path="exams" element={<Exams student={student} />} />
              <Route path="attendance" element={<Attendance student={student} />} />
              <Route path="materials" element={<CourseMaterials student={student} />} />
              <Route path="announcements" element={<Announcements student={student} />} />
              <Route path="calendar" element={<EventCalendar student={student} />} />
              <Route path="feedback" element={<Feedback student={student} />} />
              <Route path="library" element={<StudentLibrary student={student} />} />
              <Route path="grades" element={<Grades student={student} />} />
              <Route path="dissertations" element={<StudentDissertations student={student} />} />
              <Route path="*" element={
                <div className="p-4 bg-red-50 text-red-700 rounded-md">
                  Page not found. Current path: {location.pathname}
                </div>
              } />
            </Routes>
          </ErrorBoundary>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard; 