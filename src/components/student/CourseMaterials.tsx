import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  FileText, 
  Video, 
  FileAudio, 
  Image as ImageIcon, 
  Link as LinkIcon, 
  Download, 
  Search, 
  BookOpen,
  File,
  FolderOpen,
  Clock,
  Loader2,
  AlertCircle
} from "lucide-react";
import { Student } from "@/types/student";
import { collection, query, where, getDocs, Timestamp, orderBy, limit } from "firebase/firestore";
import { db } from "@/integrations/firebase/client";
import { getStorage, ref, getDownloadURL } from "firebase/storage";

interface CourseMaterialsProps {
  student: Student | null;
}

interface Material {
  id: string;
  title: string;
  description: string;
  subject: string;
  type: string;
  fileType: string;
  size: string;
  uploadDate: Timestamp;
  downloadCount: number;
  path: string;
  duration?: string;
}

interface Subject {
  id: string;
  name: string;
}

const CourseMaterials = ({ student }: CourseMaterialsProps) => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedSubject, setSelectedSubject] = useState<string>("all");
  const [materials, setMaterials] = useState<Material[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const materialTypes = [
    { id: "textbooks", name: "Textbooks", icon: <BookOpen className="h-5 w-5" /> },
    { id: "notes", name: "Lecture Notes", icon: <FileText className="h-5 w-5" /> },
    { id: "videos", name: "Video Lessons", icon: <Video className="h-5 w-5" /> },
    { id: "audio", name: "Audio Resources", icon: <FileAudio className="h-5 w-5" /> },
    { id: "worksheets", name: "Worksheets", icon: <File className="h-5 w-5" /> }
  ];
  
  useEffect(() => {
    const fetchMaterialsData = async () => {
      if (!student?.id) {
        setLoading(false);
        setError('Student information not available');
        return;
      }

      try {
        setLoading(true);
        
        // First check if the student_courses collection exists
        const studentCoursesCollectionRef = collection(db, 'student_courses');
        const testStudentCoursesQuery = query(studentCoursesCollectionRef, limit(1));
        const testStudentCoursesSnapshot = await getDocs(testStudentCoursesQuery);
        
        if (testStudentCoursesSnapshot.empty) {
          console.log('Student courses collection is empty or does not exist');
          setError('No course data found');
          setMaterials([]);
          setSubjects([]);
          setLoading(false);
          return;
        }
        
        // Fetch the student's courses
        console.log('Fetching courses for student:', student.id);
        const coursesQuery = query(
          studentCoursesCollectionRef,
          where('student_id', '==', student.id)
        );
        
        const coursesSnapshot = await getDocs(coursesQuery);
        console.log('Found courses:', coursesSnapshot.size);
        
        if (coursesSnapshot.empty) {
          console.log('No courses found for student:', student.id);
          setError('No courses found for your account');
          setMaterials([]);
          setSubjects([]);
          setLoading(false);
          return;
        }
        
        const courseIds = coursesSnapshot.docs.map(doc => doc.data().course_id);
        
        // Check if the subjects collection exists
        const subjectsCollectionRef = collection(db, 'subjects');
        const testSubjectsQuery = query(subjectsCollectionRef, limit(1));
        const testSubjectsSnapshot = await getDocs(testSubjectsQuery);
        
        let subjectsData: Subject[] = [];
        
        if (!testSubjectsSnapshot.empty) {
          // If there are too many course IDs, we need to batch the queries
          // Firestore "in" queries are limited to 10 values
          const batchSize = 10;
          
          for (let i = 0; i < courseIds.length; i += batchSize) {
            const batchIds = courseIds.slice(i, i + batchSize);
            const subjectsQuery = query(
              subjectsCollectionRef,
              where('courseId', 'in', batchIds)
            );
            
            const subjectsSnapshot = await getDocs(subjectsQuery);
            const batchData = subjectsSnapshot.docs.map(doc => ({
              id: doc.id,
              name: doc.data().name
            }));
            
            subjectsData = [...subjectsData, ...batchData];
          }
          
          console.log('Found subjects:', subjectsData.length);
        } else {
          console.log('Subjects collection is empty or does not exist');
        }
        
        setSubjects(subjectsData);
        
        // Check if the course_materials collection exists
        const materialsCollectionRef = collection(db, 'course_materials');
        const testMaterialsQuery = query(materialsCollectionRef, limit(1));
        const testMaterialsSnapshot = await getDocs(testMaterialsQuery);
        
        let materialsData: Material[] = [];
        
        if (!testMaterialsSnapshot.empty) {
          // If there are too many course IDs, we need to batch the queries
          const batchSize = 10;
          
          for (let i = 0; i < courseIds.length; i += batchSize) {
            const batchIds = courseIds.slice(i, i + batchSize);
            const materialsQuery = query(
              materialsCollectionRef,
              where('courseId', 'in', batchIds),
              orderBy('uploadDate', 'desc')
            );
            
            const materialsSnapshot = await getDocs(materialsQuery);
            const batchData = materialsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            })) as unknown as Material[];
            
            materialsData = [...materialsData, ...batchData];
          }
          
          console.log('Found materials:', materialsData.length);
        } else {
          console.log('Course materials collection is empty or does not exist');
        }
        
        setMaterials(materialsData);
        
        if (subjectsData.length === 0 && materialsData.length === 0) {
          setError('No course materials found for your account');
        } else {
          setError(null);
        }
      } catch (err) {
        console.error('Error fetching course materials:', err);
        setError('Failed to load course materials. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMaterialsData();
  }, [student?.id]);

  const formatDate = (timestamp: Timestamp) => {
    try {
      const date = timestamp.toDate();
      return date.toLocaleDateString('en-US', { 
        year: 'numeric',
        month: 'short', 
        day: 'numeric'
      });
    } catch (e) {
      return 'Invalid date';
    }
  };

  const getFileIcon = (fileType: string, type: string) => {
    if (type === "textbooks") return <BookOpen className="h-10 w-10 text-blue-600" />;
    if (type === "notes") return <FileText className="h-10 w-10 text-green-600" />;
    if (type === "videos") return <Video className="h-10 w-10 text-red-600" />;
    if (type === "audio") return <FileAudio className="h-10 w-10 text-purple-600" />;
    if (type === "worksheets") return <File className="h-10 w-10 text-amber-600" />;
    
    switch (fileType) {
      case "pdf":
        return <FileText className="h-10 w-10 text-red-600" />;
      case "mp4":
        return <Video className="h-10 w-10 text-blue-600" />;
      case "mp3":
        return <FileAudio className="h-10 w-10 text-green-600" />;
      case "jpg":
      case "png":
        return <ImageIcon className="h-10 w-10 text-purple-600" />;
      default:
        return <File className="h-10 w-10 text-gray-600" />;
    }
  };

  const getFileTypeBadge = (fileType: string) => {
    switch (fileType) {
      case "pdf":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">PDF</Badge>;
      case "mp4":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Video</Badge>;
      case "mp3":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Audio</Badge>;
      case "jpg":
      case "png":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Image</Badge>;
      default:
        return <Badge variant="outline">{fileType.toUpperCase()}</Badge>;
    }
  };
  
  const handleDownload = async (material: Material) => {
    try {
      const storage = getStorage();
      const fileRef = ref(storage, material.path);
      const url = await getDownloadURL(fileRef);
      
      // Open the URL in a new tab
      window.open(url, '_blank');
      
      // Update download count in Firestore (in a real app)
      // await updateDoc(doc(db, 'course_materials', material.id), {
      //   downloadCount: increment(1)
      // });
    } catch (err) {
      console.error('Error downloading file:', err);
      alert('Failed to download file. Please try again later.');
    }
  };

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = searchQuery === "" || 
      material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesSubject = selectedSubject === "all" || material.subject === subjects.find(s => s.id === selectedSubject)?.name;
    
    return matchesSearch && matchesSubject;
  });

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading course materials...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-red-500" />
        <p className="mt-4 text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Course Materials</h1>
        <p className="text-muted-foreground">Access study resources, textbooks, and learning materials.</p>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search materials..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div>
          <Tabs defaultValue="all" className="w-full" onValueChange={setSelectedSubject}>
            <TabsList className="grid grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              {subjects.map((subject) => (
                <TabsTrigger key={subject.id} value={subject.id}>
                  {subject.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredMaterials.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-muted-foreground">No materials found matching your criteria.</p>
          </div>
        ) : (
          filteredMaterials.map((material) => (
            <Card key={material.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    {getFileIcon(material.fileType, material.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium truncate">{material.title}</h3>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">{material.description}</p>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {getFileTypeBadge(material.fileType)}
                      <Badge variant="outline" className="bg-gray-100">{material.size}</Badge>
                      {material.duration && (
                        <Badge variant="outline" className="bg-gray-100">
                          <Clock className="h-3 w-3 mr-1" />
                          {material.duration}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-xs text-gray-500">
                        Uploaded: {formatDate(material.uploadDate)}
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDownload(material)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default CourseMaterials; 