import { useState, useEffect } from "react";
import { Student } from "@/types/student";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { User, Mail, Phone, MapPin, Calendar, Globe, School, Users } from "lucide-react";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/integrations/firebase/client";

interface StudentProfileProps {
  student: Student | null;
}

const StudentProfile = ({ student }: StudentProfileProps) => {
  const [courseDetails, setCourseDetails] = useState({
    loading: false,
    name: '',
    code: '',
    description: ''
  });
  const [levelDetails, setLevelDetails] = useState({
    loading: false,
    name: '',
    code: '',
    description: ''
  });

  useEffect(() => {
    if (!student) return;

    // Fetch course details if we only have the ID
    if (student.course_id && (!student.course || !student.course.name)) {
      setCourseDetails(prev => ({ ...prev, loading: true }));
      const fetchCourse = async () => {
        try {
          const courseDoc = await getDoc(doc(db, 'courses', student.course_id));
          if (courseDoc.exists()) {
            const courseData = courseDoc.data();
            setCourseDetails({
              loading: false,
              name: courseData.name || 'Unknown Course',
              code: courseData.code || '',
              description: courseData.description || ''
            });
          } else {
            setCourseDetails({
              loading: false,
              name: `Course ID: ${student.course_id.substring(0, 6)}...`,
              code: '',
              description: 'Course details not found'
            });
          }
        } catch (error) {
          console.error('Error fetching course:', error);
          setCourseDetails({
            loading: false,
            name: `Course ID: ${student.course_id.substring(0, 6)}...`,
            code: '',
            description: 'Error loading course details'
          });
        }
      };
      fetchCourse();
    }

    // Fetch level details if we only have the ID
    if (student.level_id && (!student.level || !student.level.name)) {
      setLevelDetails(prev => ({ ...prev, loading: true }));
      const fetchLevel = async () => {
        try {
          const levelDoc = await getDoc(doc(db, 'levels', student.level_id));
          if (levelDoc.exists()) {
            const levelData = levelDoc.data();
            setLevelDetails({
              loading: false,
              name: levelData.name || 'Unknown Level',
              code: levelData.code || '',
              description: levelData.description || ''
            });
          } else {
            setLevelDetails({
              loading: false,
              name: `Level ID: ${student.level_id.substring(0, 6)}...`,
              code: '',
              description: 'Level details not found'
            });
          }
        } catch (error) {
          console.error('Error fetching level:', error);
          setLevelDetails({
            loading: false,
            name: `Level ID: ${student.level_id.substring(0, 6)}...`,
            code: '',
            description: 'Error loading level details'
          });
        }
      };
      fetchLevel();
    }
  }, [student]);

  // Helper function to get course name
  const getCourseName = () => {
    if (!student) return "Not Available";
    
    if (student.course?.name) {
      return student.course.name;
    } else if (courseDetails.loading) {
      return "Loading...";
    } else if (courseDetails.name) {
      return courseDetails.name;
    } else if (student.course_id) {
      return `Course ID: ${student.course_id.substring(0, 6)}...`;
    } else {
      return "Not Assigned";
    }
  };

  // Helper function to get level name
  const getLevelName = () => {
    if (!student) return "Not Available";
    
    if (student.level?.name) {
      return student.level.name;
    } else if (levelDetails.loading) {
      return "Loading...";
    } else if (levelDetails.name) {
      return levelDetails.name;
    } else if (student.level_id) {
      return `Level ID: ${student.level_id.substring(0, 6)}...`;
    } else {
      return "Not Assigned";
    }
  };

  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">My Profile</h1>
          <p className="text-muted-foreground">View your personal information.</p>
        </div>
      </div>

      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="grid w-full md:w-[400px] grid-cols-2">
          <TabsTrigger value="personal">Personal Information</TabsTrigger>
          <TabsTrigger value="academic">Academic Information</TabsTrigger>
        </TabsList>
        
        <TabsContent value="personal" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Profile Summary Card */}
            <Card className="md:col-span-1">
              <CardHeader className="pb-2">
                <CardTitle>Profile</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                <Avatar className="h-32 w-32 mb-4">
                  <AvatarImage src={student.passport_picture || ''} alt={student.name} />
                  <AvatarFallback>{student.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-bold">{student.name}</h2>
                <p className="text-sm text-gray-500 mb-4">Student ID: {student.student_id}</p>
                
                <div className="w-full space-y-3">
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm">{student.gender}</span>
                  </div>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm truncate">{student.parent_email}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm">{student.mobile_number}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm truncate">{student.address}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm">DOB: {student.date_of_birth}</span>
                  </div>
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm">{student.nationality}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Personal Details Card */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Personal Details</CardTitle>
                <CardDescription>Your personal information and contact details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Full Name</h3>
                      <p>{student.name}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Date of Birth</h3>
                      <p>{student.date_of_birth}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Gender</h3>
                      <p>{student.gender}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Nationality</h3>
                      <p>{student.nationality}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Mobile Number</h3>
                      <p>{student.mobile_number}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">WhatsApp Number</h3>
                      <p>{student.whatsapp_number}</p>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Address</h3>
                    <p>{student.address}</p>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <h3 className="text-base font-medium mb-4">Parent/Guardian Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Parent Name</h3>
                        <p>{student.parent_name}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Parent Occupation</h3>
                        <p>{student.parent_occupation}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Parent Mobile</h3>
                        <p>{student.parent_mobile}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Parent WhatsApp</h3>
                        <p>{student.parent_whatsapp}</p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-500">Parent Email</h3>
                      <p>{student.parent_email}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="academic" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Academic Summary Card */}
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Academic Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <School className="h-5 w-5 text-green-600 mr-3" />
                    <div>
                      <h3 className="text-sm font-medium">Current Course</h3>
                      <p className="text-sm text-gray-500">
                        {courseDetails.loading ? (
                          <span className="text-gray-400">Loading...</span>
                        ) : (
                          getCourseName()
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <h3 className="text-sm font-medium">Current Level</h3>
                      <p className="text-sm text-gray-500">
                        {levelDetails.loading ? (
                          <span className="text-gray-400">Loading...</span>
                        ) : (
                          getLevelName()
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <h3 className="text-sm font-medium">Registration Date</h3>
                      <p className="text-sm text-gray-500">{student.date_of_registration}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Academic Details Card */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Academic Details</CardTitle>
                <CardDescription>Your enrollment and course information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-base font-medium mb-4">Course Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Course Name</h3>
                        <p>
                          {courseDetails.loading ? (
                            <span className="text-gray-400">Loading...</span>
                          ) : (
                            getCourseName()
                          )}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Course Code</h3>
                        <p>
                          {courseDetails.loading ? (
                            <span className="text-gray-400">Loading...</span>
                          ) : (
                            student.course?.code || courseDetails.code || 'N/A'
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-500">Course Description</h3>
                      <p className="text-sm">
                        {courseDetails.loading ? (
                          <span className="text-gray-400">Loading...</span>
                        ) : (
                          student.course?.description || courseDetails.description || 'No description available'
                        )}
                      </p>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <h3 className="text-base font-medium mb-4">Level Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Level Name</h3>
                        <p>
                          {levelDetails.loading ? (
                            <span className="text-gray-400">Loading...</span>
                          ) : (
                            getLevelName()
                          )}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Level Code</h3>
                        <p>
                          {levelDetails.loading ? (
                            <span className="text-gray-400">Loading...</span>
                          ) : (
                            student.level?.code || levelDetails.code || 'N/A'
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-500">Level Description</h3>
                      <p className="text-sm">
                        {levelDetails.loading ? (
                          <span className="text-gray-400">Loading...</span>
                        ) : (
                          student.level?.description || levelDetails.description || 'No description available'
                        )}
                      </p>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <h3 className="text-base font-medium mb-4">Enrollment Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Enrollment Status</h3>
                        <p className={student.enrollment_status === 'Active' ? 'text-green-600 font-medium' : 'text-gray-600'}>
                          {student.enrollment_status}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Payment Status</h3>
                        <p className={student.payment_status === 'Paid' ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
                          {student.payment_status}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Registration Date</h3>
                        <p>{student.date_of_registration}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StudentProfile;