import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  BookOpen, 
  CalendarDays, 
  FileText, 
  BarChart2,
  Bell,
  Clock,
  MapPin,
  Calendar
} from "lucide-react";
import { Student } from "@/types/student";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { collection, query, where, getDocs, Timestamp, orderBy, limit, doc, getDoc } from "firebase/firestore";
import { db } from "@/integrations/firebase/client";
import { useQuery } from "@tanstack/react-query";
import { getEvents } from "@/api/events";
import { getClassTimetableByLevel, getExamTimetableByLevel } from "@/api/timetable";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { getSubjects } from "@/api/subjects";

interface StudentOverviewProps {
  student: Student | null;
}

interface AssignmentStats {
  pending: number;
  total: number;
  loading: boolean;
  error: string | null;
}

interface AttendanceStats {
  percentage: number;
  loading: boolean;
  error: string | null;
}

interface Assignment {
  id: string;
  student_id: string;
  due_date: Timestamp;
  submitted: boolean;
  [key: string]: any;
}

interface AttendanceRecord {
  id: string;
  student_id: string;
  date: Timestamp;
  status: 'present' | 'absent' | 'late' | 'excused';
  [key: string]: any;
}

const StudentOverview = ({ student }: StudentOverviewProps) => {
  const navigate = useNavigate();
  const [assignmentStats, setAssignmentStats] = useState<AssignmentStats>({
    pending: 0,
    total: 0,
    loading: true,
    error: null
  });
  const [attendanceStats, setAttendanceStats] = useState<AttendanceStats>({
    percentage: 0,
    loading: true,
    error: null
  });
  const [courseDetails, setCourseDetails] = useState<{name: string, loading: boolean}>({
    name: '',
    loading: false
  });
  const [levelDetails, setLevelDetails] = useState<{name: string, loading: boolean}>({
    name: '',
    loading: false
  });
  const [subjectsData, setSubjectsData] = useState<any[]>([]);
  
  // Fetch upcoming events
  const { data: events = [] } = useQuery({
    queryKey: ["events-overview"],
    queryFn: getEvents,
    select: (data) => {
      // Get today's date in ISO format
      const today = format(new Date(), "yyyy-MM-dd");
      
      // Filter for upcoming events and sort by date
      return data
        .filter(event => event.date >= today)
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(0, 3); // Get only the next 3 events
    }
  });
  
  // Helper function to get level ID
  const getStudentLevelId = () => {
    if (student?.level?.id) {
      return student.level.id;
    } else if (student?.level_id) {
      return student.level_id;
    }
    return "";
  };

  const studentLevelId = getStudentLevelId();
  
  // Fetch class timetable for today
  const { data: classTimetable = [] } = useQuery({
    queryKey: ["class-timetable-overview", studentLevelId],
    queryFn: () => getClassTimetableByLevel(studentLevelId),
    enabled: !!studentLevelId,
    select: (data) => {
      // Get today's day name
      const today = format(new Date(), "EEEE");
      
      // Filter for today's classes and sort by start time
      return data
        .filter(entry => entry.day === today)
        .sort((a, b) => {
          // Convert time strings to comparable values
          const timeA = a.startTime.replace("AM", "").replace("PM", "").trim();
          const timeB = b.startTime.replace("AM", "").replace("PM", "").trim();
          return timeA.localeCompare(timeB);
        })
        .slice(0, 3); // Get only the next 3 classes
    }
  });

  // Fetch subjects
  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const subjects = await getSubjects();
        setSubjectsData(subjects);
      } catch (error) {
        console.error("Error fetching subjects:", error);
      }
    };
    
    fetchSubjects();
  }, []);

  useEffect(() => {
    const fetchAssignmentStats = async () => {
      if (!student?.id) return;

      try {
        const now = new Date();
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        
        const endOfWeek = new Date(now);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);

        // First, check if the student has a level_id
        const levelId = student.level_id || (student.level && student.level.id);
        if (!levelId) {
          console.log('Student has no level_id:', student.id);
          setAssignmentStats({
            pending: 0,
            total: 0,
            loading: false,
            error: 'Student not assigned to any level'
          });
          return;
        }

        // Get assignments for the student's level
        // Note: Firestore doesn't support multiple inequality filters on different fields
        // So we'll get assignments by level_id and filter by date in memory
        const assignmentsQuery = query(
          collection(db, 'assignments'),
          where('level_id', '==', levelId)
        );

        const snapshot = await getDocs(assignmentsQuery);
        let assignments = snapshot.docs.map(doc => ({ 
          id: doc.id, 
          ...doc.data() 
        }));
        
        // Filter assignments by date range in memory
        assignments = assignments.filter(assignment => {
          const dueDate = assignment.due_date;
          if (!dueDate) return false;
          
          // Handle different date formats
          let dueDateObj;
          if (dueDate instanceof Timestamp) {
            dueDateObj = dueDate.toDate();
          } else if (typeof dueDate === 'string') {
            dueDateObj = new Date(dueDate);
          } else {
            return false;
          }
          
          return dueDateObj >= startOfWeek && dueDateObj <= endOfWeek;
        });
        
        // Check which assignments have been submitted by this student
        const submittedAssignmentIds = new Set();
        
        if (assignments.length > 0) {
          try {
            // For small number of assignments, we can use a simple query
            if (assignments.length <= 10) {
              const submissionsQuery = query(
                collection(db, 'assignment_submissions'),
                where('student_id', '==', student.id),
                where('assignment_id', 'in', assignments.map(a => a.id))
              );
              
              const submissionsSnapshot = await getDocs(submissionsQuery);
              submissionsSnapshot.docs.forEach(doc => {
                submittedAssignmentIds.add(doc.data().assignment_id);
              });
            } else {
              // For larger number of assignments, we need to batch queries
              // Firestore 'in' operator is limited to 10 values
              for (let i = 0; i < assignments.length; i += 10) {
                const batch = assignments.slice(i, i + 10);
                const batchQuery = query(
                  collection(db, 'assignment_submissions'),
                  where('student_id', '==', student.id),
                  where('assignment_id', 'in', batch.map(a => a.id))
                );
                
                const batchSnapshot = await getDocs(batchQuery);
                batchSnapshot.docs.forEach(doc => {
                  submittedAssignmentIds.add(doc.data().assignment_id);
                });
              }
            }
          } catch (submissionError) {
            console.error('Error fetching submissions:', submissionError);
            // Continue with what we have
          }
        }
        
        // Count pending assignments (those without submissions)
        const pending = assignments.filter(a => !submittedAssignmentIds.has(a.id)).length;
        
        setAssignmentStats({
          pending,
          total: assignments.length,
          loading: false,
          error: null
        });
      } catch (error) {
        console.error('Error fetching assignment stats:', error);
        setAssignmentStats(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to load assignment data'
        }));
      }
    };

    const fetchAttendanceStats = async () => {
      if (!student?.id) return;

      try {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        // Query attendance records
        try {
          const attendanceQuery = query(
            collection(db, 'attendance_records'),
            where('student_id', '==', student.id)
          );

          const snapshot = await getDocs(attendanceQuery);
          let records = snapshot.docs.map(doc => ({ 
            id: doc.id, 
            ...doc.data() 
          } as AttendanceRecord));
          
          // Filter by date in memory
          records = records.filter(record => {
            if (!record.date) return false;
            
            let dateObj;
            if (record.date instanceof Timestamp) {
              dateObj = record.date.toDate();
            } else if (typeof record.date === 'string') {
              dateObj = new Date(record.date);
            } else if (record.date.seconds) {
              // Handle Firestore timestamp object
              dateObj = new Date(record.date.seconds * 1000);
            } else {
              return false;
            }
            
            return dateObj >= startOfMonth && dateObj <= endOfMonth;
          });
          
          const totalClasses = records.length;
          const presentClasses = records.filter(r => 
            r.status === 'present' || r.status === 'late'
          ).length;
          
          const percentage = totalClasses > 0 ? Math.round((presentClasses / totalClasses) * 100) : 0;
          
          setAttendanceStats({
            percentage,
            loading: false,
            error: null
          });
        } catch (queryError) {
          console.error('Error querying attendance records:', queryError);
          
          // Fallback to a simpler approach
          setAttendanceStats({
            percentage: 0,
            loading: false,
            error: 'Failed to load attendance data'
          });
        }
      } catch (error) {
        console.error('Error fetching attendance stats:', error);
        setAttendanceStats(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to load attendance data'
        }));
      }
    };

    fetchAssignmentStats();
    fetchAttendanceStats();
  }, [student?.id, student?.level_id]);

  // Fetch course and level details if needed
  useEffect(() => {
    const fetchCourseAndLevelDetails = async () => {
      // Only fetch if we have IDs but no names
      if (student?.course_id && (!student.course?.name && !student.course_name)) {
        setCourseDetails({ name: '', loading: true });
        try {
          const courseRef = doc(db, 'courses', student.course_id);
          const courseSnap = await getDoc(courseRef);
          
          if (courseSnap.exists()) {
            const courseData = courseSnap.data();
            setCourseDetails({ 
              name: courseData.name || 'Unknown Course', 
              loading: false 
            });
          } else {
            setCourseDetails({ 
              name: 'Course ID: ' + student.course_id.substring(0, 6) + '...', 
              loading: false 
            });
          }
        } catch (error) {
          console.error('Error fetching course details:', error);
          setCourseDetails({ 
            name: 'Course ID: ' + student.course_id.substring(0, 6) + '...', 
            loading: false 
          });
        }
      }
      
      if (student?.level_id && (!student.level?.name && !student.level_name)) {
        setLevelDetails({ name: '', loading: true });
        try {
          const levelRef = doc(db, 'levels', student.level_id);
          const levelSnap = await getDoc(levelRef);
          
          if (levelSnap.exists()) {
            const levelData = levelSnap.data();
            setLevelDetails({ 
              name: levelData.name || 'Unknown Level', 
              loading: false 
            });
          } else {
            setLevelDetails({ 
              name: 'Level ID: ' + student.level_id.substring(0, 6) + '...', 
              loading: false 
            });
          }
        } catch (error) {
          console.error('Error fetching level details:', error);
          setLevelDetails({ 
            name: 'Level ID: ' + student.level_id.substring(0, 6) + '...', 
            loading: false 
          });
        }
      }
    };
    
    fetchCourseAndLevelDetails();
  }, [student?.course_id, student?.level_id, student?.course?.name, student?.course_name, student?.level?.name, student?.level_name]);

  // Get subject name from its ID
  const getSubjectName = (subjectId: string) => {
    const subject = subjectsData.find(s => s.id === subjectId);
    if (subject) {
      return subject.name;
    }
    return subjectId; // Return the ID as fallback
  };

  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }

  const quickLinks = [
    { 
      title: "Assignments", 
      description: "View and submit your assignments",
      icon: <FileText className="h-10 w-10 text-purple-600" />,
      path: "/dashboard/student/assignments",
      color: "bg-purple-50"
    },
    { 
      title: "Timetable", 
      description: "Check your class schedule",
      icon: <Clock className="h-10 w-10 text-blue-600" />,
      path: "/dashboard/student/timetable",
      color: "bg-blue-50"
    },
    { 
      title: "Course Materials", 
      description: "Access study resources",
      icon: <BookOpen className="h-10 w-10 text-green-600" />,
      path: "/dashboard/student/materials",
      color: "bg-green-50"
    },
    { 
      title: "Announcements", 
      description: "View important notices",
      icon: <Bell className="h-10 w-10 text-amber-600" />,
      path: "/dashboard/student/announcements",
      color: "bg-amber-50"
    }
  ];
  
  // Helper function to get category badge
  const getCategoryBadge = (category: string) => {
    switch (category) {
      case 'exam':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Exam</Badge>;
      case 'academic':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Academic</Badge>;
      case 'meeting':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Meeting</Badge>;
      case 'holiday':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Holiday</Badge>;
      case 'cultural':
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Cultural</Badge>;
      case 'sports':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Sports</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Other</Badge>;
    }
  };

  // Helper function to get course name
  const getCourseName = () => {
    if (courseDetails.name) {
      return courseDetails.name;
    } else if (student?.course?.name) {
      return student.course.name;
    } else if (student?.course_name) {
      return student.course_name;
    } else if (student?.course_id && typeof student.course_id === 'string') {
      // If we only have the ID, try to display something more user-friendly
      return 'Course ID: ' + student.course_id.substring(0, 6) + '...';
    }
    return 'Not Assigned';
  };

  // Helper function to get level name
  const getLevelName = () => {
    if (levelDetails.name) {
      return levelDetails.name;
    } else if (student?.level?.name) {
      return student.level.name;
    } else if (student?.level_name) {
      return student.level_name;
    } else if (student?.level_id && typeof student.level_id === 'string') {
      // If we only have the ID, try to display something more user-friendly
      return 'Level ID: ' + student.level_id.substring(0, 6) + '...';
    }
    return 'Not Assigned';
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Welcome, {student.name}</h1>
          <p className="text-muted-foreground">Here's an overview of your academic information and activities.</p>
        </div>
      </div>

      {/* Student Profile Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Student Profile</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <Avatar className="h-24 w-24 mb-4">
              <AvatarImage src={student.passport_picture || ''} alt={student.name} />
              <AvatarFallback>{student.name.substring(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <h2 className="text-xl font-bold">{student.name}</h2>
            <p className="text-sm text-gray-500 mb-2">ID: {student.student_id}</p>
            <div className="w-full mt-4">
              <div className="flex justify-between py-2 border-b">
                <span className="text-sm text-gray-500">Course</span>
                <span className="text-sm font-medium">{getCourseName()}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-sm text-gray-500">Level</span>
                <span className="text-sm font-medium">{getLevelName()}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-sm text-gray-500">Status</span>
                <span className={`text-sm font-medium ${
                  student.enrollment_status === 'Active' ? 'text-green-600' : 'text-gray-600'
                }`}>
                  {student.enrollment_status || 'Active'}
                </span>
              </div>
            </div>
            <Button 
              variant="outline" 
              className="mt-4 w-full"
              onClick={() => navigate('/dashboard/student/profile')}
            >
              View Full Profile
            </Button>
          </CardContent>
        </Card>

        {/* Dashboard Stats */}
        <div className="md:col-span-2 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <BookOpen className="h-4 w-4 mr-2 text-green-600" />
                  Current Course
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {courseDetails.loading ? (
                    <span className="text-gray-400">Loading...</span>
                  ) : (
                    getCourseName()
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Level: {levelDetails.loading ? (
                    <span className="text-gray-400">Loading...</span>
                  ) : (
                    getLevelName()
                  )}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <CalendarDays className="h-4 w-4 mr-2 text-blue-600" />
                  Attendance
                </CardTitle>
              </CardHeader>
              <CardContent>
                {attendanceStats.loading ? (
                  <div className="text-2xl font-bold">Loading...</div>
                ) : attendanceStats.error ? (
                  <div className="text-2xl font-bold text-red-600">Error</div>
                ) : (
                  <>
                    <div className="text-2xl font-bold">{attendanceStats.percentage}%</div>
                    <p className="text-xs text-muted-foreground">
                      This month's attendance rate
                    </p>
                  </>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-purple-600" />
                  Assignments
                </CardTitle>
              </CardHeader>
              <CardContent>
                {assignmentStats.loading ? (
                  <div className="text-2xl font-bold">Loading...</div>
                ) : assignmentStats.error ? (
                  <div className="text-2xl font-bold text-red-600">Error</div>
                ) : (
                  <>
                    <div className="text-2xl font-bold">{assignmentStats.pending}</div>
                    <p className="text-xs text-muted-foreground">
                      Pending assignments this week
                    </p>
                  </>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <BarChart2 className="h-4 w-4 mr-2 text-amber-600" />
                  Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Coming Soon</div>
                <p className="text-xs text-muted-foreground">
                  Performance metrics will be available soon
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Today's Classes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center">
                <Clock className="h-4 w-4 mr-2 text-blue-600" />
                Today's Classes
              </CardTitle>
            </CardHeader>
            <CardContent>
              {classTimetable.length === 0 ? (
                <div className="text-center py-2">
                  <p className="text-sm text-gray-500">No classes scheduled for today</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {classTimetable.map((cls) => (
                    <div key={cls.id} className="flex items-center p-2 rounded-md bg-blue-50">
                      <div className="mr-3 p-2 bg-blue-100 rounded-md">
                        <Clock className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{getSubjectName(cls.subject)}</h3>
                        <div className="flex items-center text-xs text-gray-600">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{cls.startTime} - {cls.endTime}</span>
                        </div>
                        <div className="flex items-center text-xs text-gray-600">
                          <MapPin className="h-3 w-3 mr-1" />
                          <span>{cls.room}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-2"
                    onClick={() => navigate('/dashboard/student/timetable')}
                  >
                    View Full Timetable
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Upcoming Events */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-green-600" />
                Upcoming Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              {events.length === 0 ? (
                <div className="text-center py-2">
                  <p className="text-sm text-gray-500">No upcoming events</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {events.map((event) => (
                    <div key={event.id} className="flex items-start p-2 rounded-md bg-green-50">
                      <div className="mr-3 p-2 bg-green-100 rounded-md text-center min-w-[50px]">
                        <div className="text-xs font-medium text-green-800">
                          {format(new Date(event.date), "MMM")}
                        </div>
                        <div className="text-lg font-bold text-green-800">
                          {format(new Date(event.date), "dd")}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <h3 className="font-medium text-green-800">{event.title}</h3>
                          {getCategoryBadge(event.category)}
                        </div>
                        <p className="text-xs text-gray-700 mt-1 line-clamp-2">{event.description || 'No description available'}</p>
                        <div className="flex items-center text-xs text-gray-600 mt-1">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{event.startTime} - {event.endTime}</span>
                        </div>
                        <div className="flex items-center text-xs text-gray-600">
                          <MapPin className="h-3 w-3 mr-1" />
                          <span>{event.location}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-2"
                    onClick={() => navigate('/dashboard/student/calendar')}
                  >
                    View All Events
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Links */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Quick Links</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {quickLinks.map((link, index) => (
            <Card 
              key={index} 
              className={`cursor-pointer hover:shadow-md transition-shadow ${link.color}`}
              onClick={() => navigate(link.path)}
            >
              <CardContent className="p-6 flex flex-col items-center text-center">
                {link.icon}
                <h3 className="font-medium mt-3">{link.title}</h3>
                <p className="text-xs text-gray-600 mt-1">{link.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default StudentOverview; 