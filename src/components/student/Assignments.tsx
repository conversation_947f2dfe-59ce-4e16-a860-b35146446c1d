import { useState } from "react";
import { useNavigate, Routes, Route, useParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { 
  FileText, 
  Clock, 
  Calendar, 
  CheckCircle2, 
  AlertCircle, 
  Upload, 
  Download, 
  ChevronRight,
  ArrowLeft,
  Send,
  Loader2
} from "lucide-react";
import { Student } from "@/types/student";
import { toast } from "sonner";
import { Timestamp } from "firebase/firestore";
import { getStudentAssignments, getStudentAssignment, submitAssignment, EnhancedAssignment } from "@/api/student-assignments";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { uploadFile, FOLDERS } from "@/integrations/firebase/storage";

interface AssignmentsProps {
  student: Student | null;
}

interface FileUploadState {
  file: File | null;
  uploading: boolean;
  error: string | null;
  progress: number;
}

const AssignmentList = ({ student }: AssignmentsProps) => {
  const navigate = useNavigate();
  const [filter, setFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Use React Query to fetch assignments
  const { 
    data: assignments = [], 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['studentAssignments', student?.id, student?.level_id],
    queryFn: () => student?.id && student?.level_id 
      ? getStudentAssignments(student.id, student.level_id)
      : Promise.resolve([]),
    enabled: !!student?.id && !!student?.level_id
  });

  const getStatusBadge = (status: EnhancedAssignment['status']) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Clock className="h-4 w-4 mr-1" />Pending
        </Badge>;
      case "in_progress":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
          <Upload className="h-4 w-4 mr-1" />In Progress
        </Badge>;
      case "completed":
      case "submitted":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle2 className="h-4 w-4 mr-1" />Submitted
        </Badge>;
      case "graded":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          <CheckCircle2 className="h-4 w-4 mr-1" />Graded
        </Badge>;
      case "late":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <AlertCircle className="h-4 w-4 mr-1" />Late
        </Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const getDaysRemaining = (dueDate: string) => {
    const due = new Date(dueDate);
    const now = new Date();
    const diffTime = due.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return "Overdue";
    } else if (diffDays === 0) {
      return "Due today";
    } else if (diffDays === 1) {
      return "Due tomorrow";
    } else {
      return `${diffDays} days left`;
    }
  };

  // Filter assignments based on selected filter and search term
  const filteredAssignments = assignments.filter(assignment => {
    const matchesFilter = 
      filter === "all" || 
      (filter === "pending" && assignment.status === "pending") ||
      (filter === "in_progress" && assignment.status === "in_progress") ||
      (filter === "completed" && (assignment.status === "completed" || assignment.status === "submitted" || assignment.status === "graded")) ||
      (filter === "late" && assignment.status === "late");
    
    const matchesSearch = 
      assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assignment.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <p className="font-semibold">Error loading assignments</p>
        <p>{(error as Error).message || "An unknown error occurred"}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Assignments</h2>
          <p className="text-muted-foreground">
            View and submit your assignments
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Input
            placeholder="Search assignments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:w-64"
          />
          <Tabs defaultValue="all" value={filter} onValueChange={setFilter} className="w-full sm:w-auto">
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="late">Late</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {filteredAssignments.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
          <FileText className="h-12 w-12 mb-4" />
          <p>No assignments found</p>
          <p className="text-sm">
            {filter !== "all" 
              ? `No ${filter} assignments found. Try changing the filter.` 
              : searchTerm 
                ? "No assignments match your search. Try a different search term." 
                : "You don't have any assignments yet."}
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {filteredAssignments.map((assignment) => (
            <Card key={assignment.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="flex flex-col md:flex-row">
                  <div className="flex-1 p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">{assignment.title}</h3>
                        <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                          {assignment.description}
                        </p>
                      </div>
                      <div>{getStatusBadge(assignment.status)}</div>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-4">
                      <div className="flex items-center text-sm">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>Due: {formatDate(assignment.due_date)}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{getDaysRemaining(assignment.due_date)}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>Points: {assignment.points}</span>
                      </div>
                    </div>
                    
                    {assignment.status === 'graded' && assignment.submission && (
                      <div className="mt-4">
                        <div className="flex items-center text-sm">
                          <span className="font-medium mr-2">Grade:</span>
                          <span>{assignment.submission.grade} / {assignment.points}</span>
                        </div>
                      </div>
                    )}
                    
                    <div className="mt-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Progress</span>
                        <span className="text-sm text-muted-foreground">{assignment.progress}%</span>
                      </div>
                      <Progress value={assignment.progress} className="h-2 mt-1" />
                    </div>
                  </div>
                  
                  <div className="bg-muted p-6 flex flex-col justify-center items-center md:w-48">
                    <Button 
                      onClick={() => navigate(`/dashboard/student/assignments/${assignment.id}`)}
                      className="w-full"
                    >
                      {assignment.status === 'pending' || assignment.status === 'late' ? 'Start' : 'View'}
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

const AssignmentDetail = ({ student }: AssignmentsProps) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [fileUpload, setFileUpload] = useState<FileUploadState>({
    file: null,
    uploading: false,
    error: null,
    progress: 0
  });
  const [comment, setComment] = useState("");

  // Use React Query to fetch assignment details
  const { 
    data: assignment, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['studentAssignment', id, student?.id],
    queryFn: () => id && student?.id 
      ? getStudentAssignment(id, student.id)
      : Promise.resolve(null),
    enabled: !!id && !!student?.id
  });

  // Mutation for submitting an assignment
  const submitMutation = useMutation({
    mutationFn: (data: { 
      assignment_id: string; 
      student_id: string; 
      content?: string;
      file_url?: string;
      file_name?: string;
    }) => submitAssignment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['studentAssignment', id, student?.id] });
      queryClient.invalidateQueries({ queryKey: ['studentAssignments', student?.id, student?.level_id] });
      toast.success('Assignment submitted successfully');
      setComment("");
      setFileUpload({
        file: null,
        uploading: false,
        error: null,
        progress: 0
      });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to submit assignment');
    }
  });

  const getStatusBadge = (status: EnhancedAssignment['status']) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Clock className="h-4 w-4 mr-1" />Pending
        </Badge>;
      case "in_progress":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
          <Upload className="h-4 w-4 mr-1" />In Progress
        </Badge>;
      case "completed":
      case "submitted":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle2 className="h-4 w-4 mr-1" />Submitted
        </Badge>;
      case "graded":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          <CheckCircle2 className="h-4 w-4 mr-1" />Graded
        </Badge>;
      case "late":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <AlertCircle className="h-4 w-4 mr-1" />Late
        </Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFileUpload({
        file: e.target.files[0],
        uploading: false,
        error: null,
        progress: 0
      });
    }
  };

  const handleSubmit = async () => {
    if (!student?.id || !assignment?.id) {
      toast.error('Missing student or assignment information');
      return;
    }

    try {
      setFileUpload(prev => ({ ...prev, uploading: true, error: null }));
      
      // Create submission data object
      const submissionData: {
        assignment_id: string;
        student_id: string;
        content?: string;
        file_url?: string;
        file_name?: string;
        file_id?: string;
        file_path?: string;
      } = {
        assignment_id: assignment.id,
        student_id: student.id,
      };

      // Add comment if provided
      if (comment.trim()) {
        submissionData.content = comment.trim();
      }
      
      // Upload file if selected
      if (fileUpload.file) {
        try {
          const fileName = `${Date.now()}_${fileUpload.file.name}`;
          const folderPath = `${FOLDERS.SUBMISSIONS}/${student.id}/${assignment.id}`;
          
          // Upload to Firebase Storage
          const uploadedFile = await uploadFile(
            fileUpload.file, 
            fileName, 
            folderPath, 
            fileUpload.file.type
          );
          
          // Add file info to submission data
          submissionData.file_url = uploadedFile.downloadUrl;
          submissionData.file_name = fileName;
          submissionData.file_id = uploadedFile.id;
          submissionData.file_path = uploadedFile.fullPath;
        } catch (uploadError: any) {
          console.error("File upload error:", uploadError);
          toast.error(`File upload failed: ${uploadError.message}`);
          setFileUpload(prev => ({ 
            ...prev, 
            uploading: false, 
            error: uploadError.message || 'Failed to upload file' 
          }));
          return; // Exit if file upload fails
        }
      }
      
      // Submit assignment
      submitMutation.mutate(submissionData);
    } catch (error: any) {
      console.error("Submission error:", error);
      setFileUpload(prev => ({ 
        ...prev, 
        uploading: false, 
        error: error.message || 'Failed to submit assignment' 
      }));
      toast.error(`Submission failed: ${error.message || 'Unknown error'}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error || !assignment) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <p className="font-semibold">Error loading assignment</p>
        <p>{error ? (error as Error).message : "Assignment not found"}</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => navigate('/dashboard/student/assignments')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Assignments
        </Button>
      </div>
    );
  }

  const isSubmitted = assignment.status === 'submitted' || assignment.status === 'graded';
  const submission = assignment.submission || null;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button 
          variant="outline" 
          onClick={() => navigate('/dashboard/student/assignments')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Assignments
        </Button>
        <div>{getStatusBadge(assignment.status)}</div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{assignment.title}</CardTitle>
          <CardDescription>
            Due: {formatDate(assignment.due_date)} • Points: {assignment.points}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="prose max-w-none">
            <p>{assignment.description}</p>
          </div>
          
          {/* Display attachments if any */}
          {assignment.attachments && assignment.attachments?.length > 0 && (
            <div className="border rounded-md p-4">
              <h4 className="font-medium mb-2">Attachments</h4>
              <div className="space-y-2">
                {assignment.attachments?.map((attachment, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{attachment.name}</span>
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <a href={attachment.url} target="_blank" rel="noopener noreferrer">
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </a>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Submission section */}
          {isSubmitted && submission ? (
            <div className="border rounded-md p-4">
              <h4 className="font-medium mb-2">Your Submission</h4>
              
              {submission.submitted_at && (
                <p className="text-sm text-muted-foreground mb-2">
                  Submitted on {formatDate(submission.submitted_at)}
                </p>
              )}
              
              {submission.content && (
                <div className="bg-muted p-3 rounded-md mb-3">
                  <p>{submission.content}</p>
                </div>
              )}
              
              {submission.file_url && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{submission.file_name}</span>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <a href={submission.file_url} target="_blank" rel="noopener noreferrer">
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </a>
                  </Button>
                </div>
              )}
              
              {/* Display grade and feedback if graded */}
              {assignment.status === 'graded' && submission && (
                <div className="mt-4 pt-4 border-t">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">Grade</h4>
                    <span className="font-medium">{submission.grade} / {assignment.points}</span>
                  </div>
                  
                  {submission.feedback && (
                    <div>
                      <h4 className="font-medium mb-1">Feedback</h4>
                      <div className="bg-muted p-3 rounded-md">
                        <p>{submission.feedback}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="border rounded-md p-4">
              <h4 className="font-medium mb-2">Submit Your Assignment</h4>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="comment">Comments</Label>
                  <Textarea 
                    id="comment" 
                    placeholder="Add any comments or notes about your submission..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="file">Upload File</Label>
                  <div className="mt-1">
                    <Input 
                      id="file" 
                      type="file" 
                      onChange={handleFileChange}
                    />
                    {fileUpload.file && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Selected: {fileUpload.file.name} ({Math.round(fileUpload.file.size / 1024)} KB)
                      </p>
                    )}
                    {fileUpload.error && (
                      <p className="text-sm text-red-600 mt-1">{fileUpload.error}</p>
                    )}
                  </div>
                </div>
                
                <Button 
                  onClick={handleSubmit}
                  disabled={submitMutation.isPending || fileUpload.uploading}
                  className="w-full"
                >
                  {submitMutation.isPending || fileUpload.uploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Submit Assignment
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

const Assignments = ({ student }: AssignmentsProps) => {
  return (
    <Routes>
      <Route index element={<AssignmentList student={student} />} />
      <Route path=":id" element={<AssignmentDetail student={student} />} />
    </Routes>
  );
};

export default Assignments; 