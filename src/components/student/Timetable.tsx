import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Clock, MapPin, Calendar, ChevronLeft, ChevronRight, Loader2, AlertCircle } from "lucide-react";
import { Student } from "@/types/student";
import { collection, query, where, getDocs, Timestamp, orderBy, limit } from "firebase/firestore";
import { db } from "@/integrations/firebase/client";
import { useQuery } from "@tanstack/react-query";
import { 
  getClassTimetableByLevel, 
  getExamTimetableByLevel 
} from "@/api/timetable";
import type { 
  ClassTimetableEntry, 
  ExamTimetableEntry 
} from "@/integrations/firebase/models/timetable";
import { format } from "date-fns";
import { getSubjects } from "@/api/subjects";

interface TimetableProps {
  student: Student | null;
}

interface SchoolEvent {
  id: string;
  title: string;
  date: Timestamp;
  time: string;
  location: string;
  description: string;
}

const Timetable = ({ student }: TimetableProps) => {
  const [currentWeek, setCurrentWeek] = useState<string>("current");
  const [currentView, setCurrentView] = useState<string>("week");
  const [selectedDay, setSelectedDay] = useState<string>(() => {
    // Initialize with the current day of the week
    return new Date().toLocaleDateString('en-US', { weekday: 'long' });
  });
  const [schoolEvents, setSchoolEvents] = useState<SchoolEvent[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [subjects, setSubjects] = useState<any[]>([]);
  
  // Define weekDays and timeSlots
  const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
  const timeSlots = [
    "08:00", "09:00", "10:00", "11:00", "12:00", 
    "13:00", "14:00", "15:00", "16:00", "17:00", 
    "18:00", "19:00", "20:00", "21:00"
  ];
  
  // Get the student's level ID, handling different data structures
  const getStudentLevelId = () => {
    if (student?.level?.id) {
      return student.level.id;
    } else if (student?.level_id) {
      return student.level_id;
    }
    return "";
  };

  const studentLevelId = getStudentLevelId();
  
  // Fetch class timetable data
  const { 
    data: classTimetable = [], 
    isLoading: isClassTimetableLoading 
  } = useQuery({
    queryKey: ["class-timetable", studentLevelId],
    queryFn: () => getClassTimetableByLevel(studentLevelId),
    enabled: !!studentLevelId,
  });
  
  // Debug log to check class timetable data
  useEffect(() => {
    console.log("Class Timetable Data:", classTimetable);
    console.log("Student Level ID:", studentLevelId);
  }, [classTimetable, studentLevelId]);
  
  // Fetch exam timetable data
  const { 
    data: examTimetable = [], 
    isLoading: isExamTimetableLoading 
  } = useQuery({
    queryKey: ["exam-timetable", studentLevelId],
    queryFn: () => getExamTimetableByLevel(studentLevelId),
    enabled: !!studentLevelId,
  });

  // Fetch subjects
  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const subjectsData = await getSubjects();
        console.log("Fetched subjects data:", subjectsData);
        setSubjects(subjectsData);
      } catch (error) {
        console.error('Error fetching subjects:', error);
        setError('Failed to load subjects data');
      }
    };

    fetchSubjects();
  }, []);
  
  // Update selectedDay when view changes
  useEffect(() => {
    if (currentView === "day") {
      // When switching to day view, default to the current day of the week
      const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
      // Check if today is in our weekDays array, otherwise use Monday
      const isValidDay = weekDays.find(day => 
        day.toLowerCase() === today.toLowerCase()
      );
      setSelectedDay(isValidDay || weekDays[0]);
    }
  }, [currentView, weekDays]);
  
  // Add color coding to class timetable entries
  const classSchedule = classTimetable.map(entry => {
    // Generate a color based on the subject
    const subjectHash = hashCode(entry.subject);
    const colors = [
      "bg-blue-100 border-blue-300",
      "bg-purple-100 border-purple-300",
      "bg-green-100 border-green-300",
      "bg-yellow-100 border-yellow-300",
      "bg-red-100 border-red-300",
      "bg-indigo-100 border-indigo-300",
      "bg-pink-100 border-pink-300"
    ];
    const color = colors[Math.abs(subjectHash) % colors.length];
    
    // Normalize the day format if needed
    const normalizedDay = entry.day.trim();
    
    return {
      ...entry,
      day: normalizedDay,
      color
    };
  });

  // Get subject name from ID
  const getSubjectName = (subjectId: string) => {
    // Check if we have subjects data loaded
    if (subjects.length === 0) {
      console.log("Subject data not loaded yet, returning ID:", subjectId);
      // Return a placeholder while subjects are loading
      return "Loading subject...";
    }

    const subject = subjects.find((s) => s.id === subjectId);
    
    if (subject) {
      return `${subject.name} ${subject.code ? `(${subject.code})` : ''}`;
    } else {
      // Log the missing subject for debugging
      console.log("Subject not found for ID:", subjectId);
      console.log("Available subjects:", subjects.map(s => `${s.id}: ${s.name}`));
      
      // Return a more user-friendly fallback
      return "Subject info unavailable";
    }
  };
  
  // Function to generate a hash code for a string
  function hashCode(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
  }
  
  // Fetch school events
  useEffect(() => {
    const fetchEvents = async () => {
      if (!student?.id) return;
      
      try {
        const now = Timestamp.now();
        
        // First check if the school_events collection exists and has documents
        const eventsCollectionRef = collection(db, 'events');
        const testQuery = query(eventsCollectionRef, limit(1));
        const testSnapshot = await getDocs(testQuery);
        
        if (testSnapshot.empty) {
          console.log('Events collection is empty or does not exist');
          setSchoolEvents([]);
          return;
        }
        
        const eventsQuery = query(
          eventsCollectionRef,
          where('date', '>=', format(now.toDate(), 'yyyy-MM-dd')),
          orderBy('date', 'asc'),
          limit(5)
        );
        
        const eventsSnapshot = await getDocs(eventsQuery);
        console.log('Found events:', eventsSnapshot.size);
        
        const eventsData = eventsSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            title: data.title,
            date: Timestamp.fromDate(new Date(data.date)),
            time: `${data.startTime} - ${data.endTime}`,
            location: data.location,
            description: data.description
          } as SchoolEvent;
        });
        
        setSchoolEvents(eventsData);
      } catch (err) {
        console.error('Error fetching events:', err);
        setError('Failed to load school events. Please try again later.');
        setSchoolEvents([]);
      }
    };
    
    fetchEvents();
  }, [student?.id]);
  
  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }
  
  const isLoading = isClassTimetableLoading || isExamTimetableLoading;
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }
  
  const formatDate = (timestamp: Timestamp) => {
    try {
      return timestamp.toDate().toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch (e) {
      return 'Invalid date';
    }
  };
  
  // Improve time formatting
  const formatTimeString = (timeString: string) => {
    try {
      // If the time is already in HH:MM format
      if (timeString.includes(':')) {
        const [hours, minutes] = timeString.split(':').map(Number);
        
        if (isNaN(hours) || isNaN(minutes)) {
          return timeString; // Return as is if parsing fails
        }
        
        const date = new Date();
        date.setHours(hours, minutes, 0, 0);
        
        return date.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      }
      
      // If time is in another format, return as is
      return timeString;
    } catch (error) {
      console.error('Error formatting time string:', error);
      return timeString; // Return original string on error
    }
  };

  // Get class duration in hours
  const getClassDuration = (startTime: string, endTime: string) => {
    try {
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);
      const start = startHours * 60 + startMinutes;
      const end = endHours * 60 + endMinutes;
      return Math.ceil((end - start) / 60);
    } catch (error) {
      return 1;
    }
  };

  // Helper function to normalize time strings for comparison
  const normalizeTimeString = (timeString: string): string => {
    if (!timeString) return '';
    
    // Handle time strings in AM/PM format
    if (timeString.includes('AM') || timeString.includes('PM')) {
      // Convert to 24-hour format for standardization
      try {
        const [time, period] = timeString.split(' ');
        let [hours, minutes] = time.split(':').map(Number);
        
        if (period === 'PM' && hours < 12) {
          hours += 12;
        } else if (period === 'AM' && hours === 12) {
          hours = 0;
        }
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      } catch (error) {
        console.error('Error normalizing time string:', error);
        return timeString.replace(/\s/g, '').toUpperCase();
      }
    }
    
    // If it's already in 24-hour format (HH:MM), ensure it's properly padded
    if (timeString.includes(':')) {
      try {
        const [hours, minutes] = timeString.split(':').map(Number);
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      } catch (error) {
        console.error('Error standardizing time format:', error);
      }
    }
    
    // If it's in another format, just clean it
    return timeString.replace(/\s/g, '').toUpperCase();
  };

  const getClassForTimeSlot = (day: string, timeSlot: string) => {
    // Normalize the input time slot
    const normalizedTimeSlot = normalizeTimeString(timeSlot);
    
    // Debug each comparison
    const result = classSchedule.find(cls => {
      // Normalize the day names for case-insensitive comparison
      const classDay = cls.day.trim().toLowerCase();
      const requestedDay = day.trim().toLowerCase();
      
      // Normalize the time strings
      const classStartTime = normalizeTimeString(cls.startTime);
      
      const match = classDay === requestedDay && classStartTime === normalizedTimeSlot;
      if (classDay === requestedDay) {
        console.log(`Day Match: ${classDay} vs ${requestedDay}, Time: ${classStartTime} vs ${normalizedTimeSlot}, Match: ${match}`);
      }
      
      // Compare the normalized values
      return match;
    });
    
    return result;
  };
  
  const isClassContinuation = (day: string, timeSlot: string, index: number) => {
    if (index === 0) return false;
    
    const prevTimeSlot = timeSlots[index - 1];
    const classInPrevSlot = getClassForTimeSlot(day, prevTimeSlot);
    const classInCurrentSlot = getClassForTimeSlot(day, timeSlot);
    
    return classInPrevSlot && classInCurrentSlot && 
           classInPrevSlot.subject === classInCurrentSlot.subject;
  };
  
  // Helper function to get the current day view
  const getCurrentDayView = () => {
    return selectedDay;
  };
  
  // Helper function to navigate to previous day
  const goToPreviousDay = () => {
    const currentIndex = weekDays.findIndex(
      day => day.toLowerCase() === selectedDay.toLowerCase()
    );
    const prevIndex = (currentIndex - 1 + weekDays.length) % weekDays.length;
    setSelectedDay(weekDays[prevIndex]);
  };
  
  // Helper function to navigate to next day
  const goToNextDay = () => {
    const currentIndex = weekDays.findIndex(
      day => day.toLowerCase() === selectedDay.toLowerCase()
    );
    const nextIndex = (currentIndex + 1) % weekDays.length;
    setSelectedDay(weekDays[nextIndex]);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Timetable</h1>
        <p className="text-muted-foreground">View your class schedule, upcoming exams, and school events.</p>
      </div>
      
      <Tabs defaultValue="schedule">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="schedule">Class Schedule</TabsTrigger>
          <TabsTrigger value="exams">Exams</TabsTrigger>
          <TabsTrigger value="events">School Events</TabsTrigger>
        </TabsList>
        
        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Weekly Schedule</CardTitle>
                <div className="flex space-x-2">
                  <Select value={currentWeek} onValueChange={setCurrentWeek}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Select Week" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="previous">Previous Week</SelectItem>
                      <SelectItem value="current">Current Week</SelectItem>
                      <SelectItem value="next">Next Week</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={currentView} onValueChange={setCurrentView}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Select View" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="week">Week View</SelectItem>
                      <SelectItem value="day">Day View</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {classSchedule.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-lg font-medium">No classes scheduled</p>
                  <p className="text-gray-500">Your class schedule will appear here once it's available.</p>
                </div>
              ) : currentView === "week" ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr>
                        <th className="border p-2 bg-gray-50 w-24"></th>
                        {weekDays.map(day => (
                          <th key={day} className="border p-2 bg-gray-50 font-medium">{day}</th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {timeSlots.map((timeSlot, index) => (
                        <tr key={timeSlot} className="h-24">
                          <td className="border p-2 text-center text-sm font-medium bg-gray-50">
                            {formatTimeString(timeSlot)}
                          </td>
                          {weekDays.map(day => {
                            const cls = getClassForTimeSlot(day, timeSlot);
                            const isContinuation = isClassContinuation(day, timeSlot, index);
                            
                            if (isContinuation) {
                              return <td key={`${day}-${timeSlot}`} className="border p-0"></td>;
                            }
                            
                            if (cls) {
                              const duration = getClassDuration(cls.startTime, cls.endTime);
                              const rowSpan = duration > 1 ? duration : 1;
                              
                              return (
                                <td 
                                  key={`${day}-${timeSlot}`} 
                                  className="border p-0 relative"
                                  rowSpan={rowSpan}
                                >
                                  <div className={`p-3 h-full ${cls.color} hover:shadow-md transition-shadow duration-200 rounded-md m-1`}>
                                    <div className="font-bold text-sm">{getSubjectName(cls.subject)}</div>
                                    <div className="text-xs mt-2">
                                      <div className="flex items-center text-gray-700 mb-1 font-medium">
                                        <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
                                        <span>{formatTimeString(cls.startTime)} - {formatTimeString(cls.endTime)}</span>
                                      </div>
                                      <div className="flex items-center text-gray-700 font-medium">
                                        <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                                        <span>Room {cls.room}</span>
                                      </div>
                                    </div>
                                  </div>
                                </td>
                              );
                            }
                            
                            return (
                              <td key={`${day}-${timeSlot}`} className="border p-0">
                                <div className="h-full w-full p-2"></div>
                              </td>
                            );
                          })}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                // Day View - Show only one day
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <Button variant="outline" onClick={goToPreviousDay}>
                      <ChevronLeft className="h-4 w-4 mr-1" /> Previous Day
                    </Button>
                    <div className="font-medium">
                      {selectedDay}
                    </div>
                    <Button variant="outline" onClick={goToNextDay}>
                      Next Day <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                  
                  <table className="w-full border-collapse">
                    <thead>
                      <tr>
                        <th className="border p-2 bg-gray-50 w-24">Time</th>
                        <th className="border p-2 bg-gray-50">Class</th>
                      </tr>
                    </thead>
                    <tbody>
                      {timeSlots.map((timeSlot, index) => {
                        const cls = getClassForTimeSlot(selectedDay, timeSlot);
                        const isContinuation = isClassContinuation(selectedDay, timeSlot, index);
                        
                        if (isContinuation) return null;
                        
                        return (
                          <tr key={timeSlot} className="h-16">
                            <td className="border p-2 text-center text-sm font-medium bg-gray-50">
                              {formatTimeString(timeSlot)}
                            </td>
                            <td className="border p-0">
                              {cls ? (
                                <div className={`p-3 h-full ${cls.color} hover:shadow-md transition-shadow duration-200 rounded-md m-1`}>
                                  <div className="font-bold text-sm">{getSubjectName(cls.subject)}</div>
                                  <div className="text-xs mt-2">
                                    <div className="flex items-center text-gray-700 mb-1 font-medium">
                                      <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
                                      <span>{formatTimeString(cls.startTime)} - {formatTimeString(cls.endTime)}</span>
                                    </div>
                                    <div className="flex items-center text-gray-700 font-medium">
                                      <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                                      <span>Room {cls.room}</span>
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div className="h-full w-full p-2 text-center text-gray-400">No class scheduled</div>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="exams">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Exams</CardTitle>
            </CardHeader>
            <CardContent>
              {examTimetable.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-lg font-medium">No upcoming exams</p>
                  <p className="text-gray-500">Your exam schedule will appear here once it's available.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {examTimetable.map((exam) => (
                    <Card key={exam.id} className="border border-gray-200">
                      <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row gap-4">
                          <div className="bg-red-100 text-red-800 p-3 rounded-md text-center min-w-[100px] md:min-w-[120px] flex flex-col justify-center">
                            <div className="text-xs font-medium">
                              {new Date(exam.examDate).toLocaleDateString('en-US', { month: 'short' })}
                            </div>
                            <div className="text-2xl font-bold">
                              {new Date(exam.examDate).getDate()}
                            </div>
                            <div className="text-xs">
                              {new Date(exam.examDate).toLocaleDateString('en-US', { weekday: 'short' })}
                            </div>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-lg">{getSubjectName(exam.subject)}</h3>
                            <Badge className="mt-1 bg-red-50 text-red-700 border-red-200">
                              {exam.examType}
                            </Badge>
                            <div className="flex items-center text-sm text-gray-600 mt-2">
                              <Clock className="h-4 w-4 mr-1" />
                              {exam.startTime} - {exam.endTime}
                            </div>
                            <div className="flex items-center text-sm text-gray-600 mt-1">
                              <MapPin className="h-4 w-4 mr-1" />
                              {exam.room}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>School Events</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {schoolEvents.map((event) => (
                  <Card key={event.id} className="border border-gray-200">
                    <CardContent className="p-4">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="bg-green-100 text-green-800 p-3 rounded-md text-center min-w-[100px] md:min-w-[120px] flex flex-col justify-center">
                          <div className="text-xs font-medium">
                            {new Date(event.date.toDate()).toLocaleDateString('en-US', { month: 'short' })}
                          </div>
                          <div className="text-2xl font-bold">
                            {new Date(event.date.toDate()).getDate()}
                          </div>
                          <div className="text-xs">
                            {new Date(event.date.toDate()).toLocaleDateString('en-US', { weekday: 'short' })}
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-lg">{event.title}</h3>
                          <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                          <div className="flex items-center text-sm text-gray-600 mt-2">
                            <Clock className="h-4 w-4 mr-1" />
                            {event.time}
                          </div>
                          <div className="flex items-center text-sm text-gray-600 mt-1">
                            <MapPin className="h-4 w-4 mr-1" />
                            {event.location}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                {schoolEvents.length === 0 && (
                  <div className="text-center py-8">
                    <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-lg font-medium">No upcoming events</p>
                    <p className="text-gray-500">School events will appear here once they're scheduled.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Timetable; 