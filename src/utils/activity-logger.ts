import { auth } from "@/integrations/firebase/client";
import { ActivityType } from "@/types/activity";
import { createActivityLog } from "@/integrations/firebase/models/activity";

export const logActivity = async (
  activity_type: ActivityType,
  details: Record<string, any> = {}
) => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      console.error("No user found when trying to log activity");
      return;
    }

    const activityDescription = details.description || 
      `${activity_type.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ')}`;

    const sanitizedMetadata = sanitizeMetadata(details);

    console.log("Logging activity:", {
      type: activity_type,
      user: user.uid,
      description: activityDescription
    });

    await createActivityLog({
      user_id: user.uid,
      activity_type,
      description: activityDescription,
      metadata: sanitizedMetadata,
      ip_address: window.location.hostname,
      user_agent: navigator.userAgent,
    });

    console.log("Activity logged successfully:", activity_type);
  } catch (error) {
    console.error("Error in logActivity:", error);
  }
};

const sanitizeMetadata = (data: Record<string, any>): Record<string, any> => {
  if (!data) return {};
  
  const result: Record<string, any> = {};
  
  Object.keys(data).forEach(key => {
    const value = data[key];
    
    if (typeof value === 'function' || value === undefined) {
      return;
    }
    
    if (value !== null && typeof value === 'object') {
      if (value instanceof Element) {
        return;
      }
      
      if (value instanceof Date) {
        result[key] = value.toISOString();
        return;
      }
      
      if (Array.isArray(value)) {
        result[key] = value.map(item => 
          typeof item === 'object' && item !== null 
            ? sanitizeMetadata({item})[`item`] 
            : item
        );
        return;
      }
      
      result[key] = sanitizeMetadata(value);
      return;
    }
    
    result[key] = value;
  });
  
  return result;
};
