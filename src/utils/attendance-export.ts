import * as XLSX from 'xlsx';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays } from 'date-fns';
import { collection, query, where, getDocs, Timestamp, getDoc, doc } from 'firebase/firestore';
import { db } from '@/integrations/firebase/client';
import { toast } from 'sonner';

// Types for attendance records
interface AttendanceRecord {
  id: string;
  student_id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
  course_id?: string;
  level_id?: string;
  student?: {
    id: string;
    name: string;
    student_id: string;
    [key: string]: any;
  };
}

/**
 * Get attendance records for a specified date range
 */
export const getAttendanceByDateRange = async (startDate: Date, endDate: Date, courseId?: string, levelId?: string) => {
  try {
    const formattedStartDate = format(startOfDay(startDate), 'yyyy-MM-dd');
    const formattedEndDate = format(endOfDay(endDate), 'yyyy-MM-dd');
    
    console.log(`Fetching attendance from ${formattedStartDate} to ${formattedEndDate}`);
    
    // Create query for date range
    let attendanceQuery = query(
      collection(db, 'attendance_records'),
      where('date', '>=', formattedStartDate),
      where('date', '<=', formattedEndDate)
    );
    
    // Add course filter if provided
    if (courseId) {
      attendanceQuery = query(
        collection(db, 'attendance_records'),
        where('date', '>=', formattedStartDate),
        where('date', '<=', formattedEndDate),
        where('course_id', '==', courseId)
      );
      
      // Add level filter if provided (only if course is also provided)
      if (levelId) {
        attendanceQuery = query(
          collection(db, 'attendance_records'),
          where('date', '>=', formattedStartDate),
          where('date', '<=', formattedEndDate),
          where('course_id', '==', courseId),
          where('level_id', '==', levelId)
        );
      }
    }
    
    const querySnapshot = await getDocs(attendanceQuery);
    
    // Get all attendance records
    const attendanceRecords = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as AttendanceRecord[];
    
    console.log(`Found ${attendanceRecords.length} attendance records`);
    
    // Get unique student IDs from attendance records
    const studentIds = [...new Set(attendanceRecords.map(record => record.student_id))];
    console.log(`Processing ${studentIds.length} unique students`);
    
    // Get unique course IDs from attendance records
    const courseIds = [...new Set(attendanceRecords.filter(r => r.course_id).map(r => r.course_id as string))];
    console.log(`Processing ${courseIds.length} unique courses`);
    
    // Get unique level IDs from attendance records
    const levelIds = [...new Set(attendanceRecords.filter(r => r.level_id).map(r => r.level_id as string))];
    console.log(`Processing ${levelIds.length} unique levels`);
    
    // Create a map to store student details
    const studentMap = new Map();
    
    // Handle student data in smaller batches to avoid Firestore limitations
    for (let i = 0; i < studentIds.length; i += 10) {
      const batchIds = studentIds.slice(i, Math.min(i + 10, studentIds.length));
      console.log(`Fetching details for students ${i+1} to ${i+batchIds.length}`);
      
      try {
        // Use a query if there are multiple IDs to fetch
        if (batchIds.length > 1) {
          const studentsSnapshot = await getDocs(
            query(collection(db, 'students'), where('id', 'in', batchIds))
          );
          
          studentsSnapshot.docs.forEach(doc => {
            const student = { id: doc.id, ...doc.data() };
            studentMap.set(doc.id, student);
          });
        } 
        // For a single ID, use getDoc which is more efficient
        else if (batchIds.length === 1) {
          const studentDoc = await getDoc(doc(db, 'students', batchIds[0]));
          if (studentDoc.exists()) {
            const student = { id: studentDoc.id, ...studentDoc.data() };
            studentMap.set(studentDoc.id, student);
          }
        }
      } catch (error) {
        console.error(`Error fetching student batch ${i}-${i+10}:`, error);
      }
    }
    
    // If we couldn't find students using 'id' field, try with document ID
    if (studentMap.size === 0 && studentIds.length > 0) {
      console.log("Retrying student fetch using document ID approach");
      
      for (const studentId of studentIds) {
        try {
          const studentDoc = await getDoc(doc(db, 'students', studentId));
          if (studentDoc.exists()) {
            const student = { id: studentDoc.id, ...studentDoc.data() };
            studentMap.set(studentId, student);
          }
        } catch (error) {
          console.error(`Error fetching student ${studentId}:`, error);
        }
      }
    }
    
    console.log(`Successfully retrieved details for ${studentMap.size}/${studentIds.length} students`);
    
    // Fetch course details
    const courseMap = new Map();
    for (const courseId of courseIds) {
      try {
        const courseDoc = await getDoc(doc(db, 'courses', courseId));
        if (courseDoc.exists()) {
          courseMap.set(courseId, { id: courseDoc.id, ...courseDoc.data() });
        }
      } catch (error) {
        console.error(`Error fetching course ${courseId}:`, error);
      }
    }
    
    console.log(`Successfully retrieved details for ${courseMap.size}/${courseIds.length} courses`);
    
    // Fetch level details
    const levelMap = new Map();
    for (const levelId of levelIds) {
      try {
        const levelDoc = await getDoc(doc(db, 'levels', levelId));
        if (levelDoc.exists()) {
          levelMap.set(levelId, { id: levelDoc.id, ...levelDoc.data() });
        }
      } catch (error) {
        console.error(`Error fetching level ${levelId}:`, error);
      }
    }
    
    console.log(`Successfully retrieved details for ${levelMap.size}/${levelIds.length} levels`);
    
    // Attach student, course, and level details to attendance records
    const enrichedRecords = attendanceRecords.map(record => {
      // Get student info - first try the map, then try alternatives
      let student = studentMap.get(record.student_id);
      
      // If student wasn't found by ID, try getting it directly
      if (!student && record.student_id) {
        console.log(`Student with ID ${record.student_id} not found in pre-fetched data`);
      }
      
      // Get course and level info
      const course = record.course_id ? courseMap.get(record.course_id) : null;
      const level = record.level_id ? levelMap.get(record.level_id) : null;
      
      return {
        ...record,
        student,
        course,
        level
      };
    });
    
    return enrichedRecords;
  } catch (error) {
    console.error('Error fetching attendance by date range:', error);
    throw error;
  }
};

/**
 * Export attendance records to Excel
 */
export const exportAttendanceToExcel = async (
  range: 'day' | 'week' | 'month',
  date = new Date(),
  courseId?: string,
  levelId?: string
) => {
  try {
    toast.info(`Preparing ${range} attendance export...`);
    console.log(`Starting export for range: ${range}, date: ${date}, courseId: ${courseId}, levelId: ${levelId}`);
    
    let startDate: Date;
    let endDate: Date;
    let title: string;
    
    // Set date range based on the selected range
    switch (range) {
      case 'day':
        startDate = startOfDay(date);
        endDate = endOfDay(date);
        title = `Attendance_${format(date, 'yyyy-MM-dd')}`;
        break;
      case 'week':
        startDate = startOfWeek(date, { weekStartsOn: 1 }); // Monday as week start
        endDate = endOfWeek(date, { weekStartsOn: 1 });
        title = `Attendance_Week_${format(startDate, 'yyyy-MM-dd')}_to_${format(endDate, 'yyyy-MM-dd')}`;
        break;
      case 'month':
        startDate = startOfMonth(date);
        endDate = endOfMonth(date);
        title = `Attendance_${format(date, 'yyyy-MM')}`;
        break;
      default:
        throw new Error('Invalid range specified');
    }
    
    console.log(`Date range: ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`);
    
    // Fetch attendance records for the date range
    const records = await getAttendanceByDateRange(startDate, endDate, courseId, levelId);
    console.log(`Fetched ${records?.length || 0} attendance records`);
    
    if (!records || records.length === 0) {
      toast.error(`No attendance records found for the selected ${range}`);
      return;
    }
    
    // Create workbook
    const wb = XLSX.utils.book_new();
    
    // Prepare summary sheet data
    const summaryData = [
      ['Attendance Export Summary'],
      [''],
      ['Date Range', `${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`],
      ['Total Records', records.length.toString()],
      [''],
      ['Status', 'Count', 'Percentage'],
      ['Present', records.filter(r => r.status === 'present').length.toString(), 
       `${((records.filter(r => r.status === 'present').length / records.length) * 100).toFixed(2)}%`],
      ['Absent', records.filter(r => r.status === 'absent').length.toString(),
       `${((records.filter(r => r.status === 'absent').length / records.length) * 100).toFixed(2)}%`],
      ['Late', records.filter(r => r.status === 'late').length.toString(),
       `${((records.filter(r => r.status === 'late').length / records.length) * 100).toFixed(2)}%`],
      ['Excused', records.filter(r => r.status === 'excused').length.toString(),
       `${((records.filter(r => r.status === 'excused').length / records.length) * 100).toFixed(2)}%`],
    ];
    
    console.log("Creating summary sheet");
    
    // Add summary sheet
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, summarySheet, 'Summary');
    
    // Set column widths for summary sheet
    const summaryColWidths = [
      { wch: 15 }, // Column A
      { wch: 15 }, // Column B
      { wch: 15 }, // Column C
    ];
    summarySheet['!cols'] = summaryColWidths;
    
    // Prepare attendance records sheet
    const attendanceHeaders = [
      'Date', 
      'Student ID', 
      'Student Name',
      'Course Code',
      'Course Name',
      'Level Code',
      'Level Name',
      'Status', 
      'Notes'
    ];
    
    console.log("Creating attendance records sheet");
    
    // Safely extract data for Excel, handling undefined values
    const attendanceRows = records.map((record, index) => {
      // Debug output for each record
      console.log(`Record ${index+1}/${records.length}:`, {
        recordId: record.id,
        studentId: record.student_id,
        student: record.student ? `${record.student.name} (${record.student.student_id})` : 'Missing student data',
        courseId: record.course_id,
        course: record.course ? record.course.name : 'Missing course data',
        levelId: record.level_id,
        level: record.level ? record.level.name : 'Missing level data'
      });
      
      // For student information, prioritize the student object but fall back to raw id
      const studentName = record.student?.name || 'Unknown Student';
      const studentId = record.student?.student_id || record.student_id || '';
      
      // For course information, prioritize the course object but fall back to raw id
      const courseCode = record.course?.code || '';
      const courseName = record.course?.name || (record.course_id ? `Course ID: ${record.course_id}` : '');
      
      // For level information, prioritize the level object but fall back to raw id
      const levelCode = record.level?.code || '';
      const levelName = record.level?.name || (record.level_id ? `Level ID: ${record.level_id}` : '');
      
      return [
        record.date || '',
        studentId,
        studentName,
        courseCode,
        courseName,
        levelCode,
        levelName,
        record.status || '',
        record.notes || ''
      ];
    });
    
    const attendanceData = [attendanceHeaders, ...attendanceRows];
    
    // Add attendance sheet
    const attendanceSheet = XLSX.utils.aoa_to_sheet(attendanceData);
    XLSX.utils.book_append_sheet(wb, attendanceSheet, 'Attendance Records');
    
    // Set column widths for attendance sheet
    const attendanceColWidths = [
      { wch: 12 }, // Date
      { wch: 12 }, // Student ID
      { wch: 25 }, // Student Name
      { wch: 12 }, // Course Code
      { wch: 25 }, // Course Name
      { wch: 12 }, // Level Code
      { wch: 25 }, // Level Name
      { wch: 10 }, // Status
      { wch: 30 }, // Notes
    ];
    attendanceSheet['!cols'] = attendanceColWidths;
    
    console.log("Preparing to write Excel file");
    
    // Write to file and download
    XLSX.writeFile(wb, `${title}.xlsx`);
    
    console.log("Excel file written successfully");
    toast.success(`Attendance exported successfully for the ${range}`);
  } catch (error) {
    console.error(`Error exporting ${range} attendance:`, error);
    toast.error(`Failed to export attendance for the ${range}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}; 