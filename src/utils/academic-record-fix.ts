import { db } from '@/integrations/firebase/client';
import { collection, getDocs, query, where, deleteDoc, doc, addDoc } from 'firebase/firestore';
import * as examResultModel from '@/integrations/firebase/models/exam-result';
import * as studentModel from '@/integrations/firebase/models/student';
import * as examModel from '@/integrations/firebase/models/exam';

/**
 * Utility to fix academic records by deleting and recreating them
 * This ensures that student_id references work correctly even with non-generated IDs
 */

export const deleteAllExamResults = async (): Promise<number> => {
  try {
    const examResultsRef = collection(db, 'exam_results');
    const snapshot = await getDocs(examResultsRef);
    
    let count = 0;
    const deletePromises = snapshot.docs.map(async (document) => {
      await deleteDoc(doc(db, 'exam_results', document.id));
      count++;
    });
    
    await Promise.all(deletePromises);
    console.log(`Successfully deleted ${count} exam results`);
    return count;
  } catch (error) {
    console.error('Error deleting exam results:', error);
    throw error;
  }
};

export const deleteExamResultsByStudentId = async (studentId: string): Promise<number> => {
  try {
    const examResultsRef = collection(db, 'exam_results');
    const q = query(examResultsRef, where('student_id', '==', studentId));
    const snapshot = await getDocs(q);
    
    let count = 0;
    const deletePromises = snapshot.docs.map(async (document) => {
      await deleteDoc(doc(db, 'exam_results', document.id));
      count++;
    });
    
    await Promise.all(deletePromises);
    console.log(`Successfully deleted ${count} exam results for student ${studentId}`);
    return count;
  } catch (error) {
    console.error(`Error deleting exam results for student ${studentId}:`, error);
    throw error;
  }
};

export const createSampleExamResults = async (studentId: string): Promise<string[]> => {
  try {
    // First, verify that the student exists
    const student = await studentModel.getStudentByStudentId(studentId);
    if (!student) {
      throw new Error(`Student with ID ${studentId} not found`);
    }
    
    // Get some exams to use for sample results
    const exams = await examModel.getAllExams();
    if (exams.length === 0) {
      throw new Error('No exams found to create sample results');
    }
    
    // Create sample exam results
    const sampleResults = [];
    
    // Use up to 5 exams for sample results
    const examCount = Math.min(exams.length, 5);
    
    for (let i = 0; i < examCount; i++) {
      const exam = exams[i];
      // Generate random marks between 60 and 100
      const marks = Math.floor(Math.random() * 41) + 60;
      
      // Determine grade based on marks
      let grade;
      if (marks >= 90) grade = 'A';
      else if (marks >= 80) grade = 'B';
      else if (marks >= 70) grade = 'C';
      else if (marks >= 60) grade = 'D';
      else grade = 'F';
      
      const result = {
        exam_id: exam.id,
        student_id: studentId,
        marks,
        grade,
        remarks: `Sample result for ${exam.name}`,
        date_recorded: new Date().toISOString(),
        recorded_by: 'system'
      };
      
      sampleResults.push(result);
    }
    
    // Bulk create the exam results
    const resultIds = await examResultModel.bulkCreateExamResults(sampleResults);
    console.log(`Successfully created ${resultIds.length} sample exam results for student ${studentId}`);
    
    return resultIds;
  } catch (error) {
    console.error(`Error creating sample exam results for student ${studentId}:`, error);
    throw error;
  }
};

// Main function to fix academic records for a specific student
export const fixAcademicRecords = async (studentId: string): Promise<void> => {
  try {
    console.log(`Starting academic record fix for student ${studentId}`);
    
    // Step 1: Delete existing exam results for this student
    const deletedCount = await deleteExamResultsByStudentId(studentId);
    console.log(`Deleted ${deletedCount} existing exam results`);
    
    // Step 2: Create new sample exam results
    const newResultIds = await createSampleExamResults(studentId);
    console.log(`Created ${newResultIds.length} new exam results`);
    
    console.log('Academic record fix completed successfully');
  } catch (error) {
    console.error('Error fixing academic records:', error);
    throw error;
  }
};

// Utility function to fix all students' academic records
export const fixAllStudentsAcademicRecords = async (): Promise<void> => {
  try {
    // Step 1: Get all students
    const students = await studentModel.getAllStudents();
    console.log(`Found ${students.length} students`);
    
    // Step 2: Delete all existing exam results
    await deleteAllExamResults();
    
    // Step 3: Create new sample exam results for each student
    for (const student of students) {
      await createSampleExamResults(student.student_id);
      console.log(`Created sample results for ${student.name} (${student.student_id})`);
    }
    
    console.log('Fixed academic records for all students');
  } catch (error) {
    console.error('Error fixing all students academic records:', error);
    throw error;
  }
}; 