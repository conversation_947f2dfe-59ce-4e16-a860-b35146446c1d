import * as XLSX from 'xlsx';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { collection, query, where, getDocs, getDoc, doc } from 'firebase/firestore';
import { db } from '@/integrations/firebase/client';
import { toast } from 'sonner';

/**
 * Simple attendance export that directly queries all relevant data without requiring complex indices
 */
export const exportAttendanceToExcel = async (
  range: 'day' | 'week' | 'month',
  date = new Date(),
  courseId?: string,
  levelId?: string
) => {
  try {
    toast.info(`Preparing ${range} attendance export...`);
    
    // 1. Determine date range
    let startDate: Date;
    let endDate: Date;
    let title: string;
    
    switch (range) {
      case 'day':
        startDate = startOfDay(date);
        endDate = endOfDay(date);
        title = `Attendance_${format(date, 'yyyy-MM-dd')}`;
        break;
      case 'week':
        startDate = startOfWeek(date, { weekStartsOn: 1 }); // Monday as week start
        endDate = endOfWeek(date, { weekStartsOn: 1 });
        title = `Attendance_Week_${format(startDate, 'yyyy-MM-dd')}_to_${format(endDate, 'yyyy-MM-dd')}`;
        break;
      case 'month':
        startDate = startOfMonth(date);
        endDate = endOfMonth(date);
        title = `Attendance_${format(date, 'yyyy-MM')}`;
        break;
      default:
        throw new Error('Invalid range specified');
    }
    
    const formattedStartDate = format(startDate, 'yyyy-MM-dd');
    const formattedEndDate = format(endDate, 'yyyy-MM-dd');
    
    console.log(`Exporting attendance from ${formattedStartDate} to ${formattedEndDate}`);
    
    // 2. Query attendance records - only filter by date to avoid needing composite indices
    const attendanceQuery = query(
      collection(db, 'attendance_records'),
      where('date', '>=', formattedStartDate),
      where('date', '<=', formattedEndDate)
    );
    
    // Execute query
    const querySnapshot = await getDocs(attendanceQuery);
    
    if (querySnapshot.empty) {
      toast.error(`No attendance records found for the selected ${range}`);
      return;
    }
    
    // 3. Process attendance records and collect related data
    const allRecords = [];
    const processedStudents = new Map();
    const processedCourses = new Map();
    const processedLevels = new Map();
    
    // First pass: collect all records
    for (const doc of querySnapshot.docs) {
      const data = doc.data();
      allRecords.push({
        id: doc.id,
        date: data.date || '',
        status: data.status || '',
        notes: data.notes || '',
        student_id: data.student_id || '',
        course_id: data.course_id || '',
        level_id: data.level_id || '',
        student_name: '', // Will be filled in later
        student_number: '', // Will be filled in later
        course_name: '', // Will be filled in later
        course_code: '', // Will be filled in later
        level_name: '', // Will be filled in later
        level_code: '' // Will be filled in later
      });
    }
    
    console.log(`Found ${allRecords.length} attendance records total`);
    
    // Filter records locally instead of in the query to avoid composite index issues
    const records = allRecords.filter(record => {
      // If no filters, include all records
      if (!courseId && !levelId) return true;
      
      // Filter by course if specified
      if (courseId && record.course_id !== courseId) return false;
      
      // Filter by level if specified
      if (levelId && record.level_id !== levelId) return false;
      
      return true;
    });
    
    console.log(`After filtering: ${records.length} attendance records match criteria`);
    
    if (records.length === 0) {
      toast.error(`No attendance records found matching the selected criteria`);
      return;
    }
    
    // 4. Fetch student data - One by one to avoid 'in' query limitations
    for (const record of records) {
      if (record.student_id && !processedStudents.has(record.student_id)) {
        try {
          const studentDoc = await getDoc(doc(db, 'students', record.student_id));
          if (studentDoc.exists()) {
            const studentData = studentDoc.data();
            processedStudents.set(record.student_id, {
              name: studentData.name || 'Unknown',
              student_id: studentData.student_id || record.student_id
            });
          } else {
            console.log(`Student document not found for ID: ${record.student_id}`);
            processedStudents.set(record.student_id, {
              name: 'Unknown Student',
              student_id: record.student_id
            });
          }
        } catch (error) {
          console.error(`Error fetching student data for ${record.student_id}:`, error);
          processedStudents.set(record.student_id, {
            name: 'Unknown Student',
            student_id: record.student_id
          });
        }
      }
    }
    
    // 5. Fetch course data - One by one
    for (const record of records) {
      if (record.course_id && !processedCourses.has(record.course_id)) {
        try {
          const courseDoc = await getDoc(doc(db, 'courses', record.course_id));
          if (courseDoc.exists()) {
            const courseData = courseDoc.data();
            processedCourses.set(record.course_id, {
              name: courseData.name || 'Unknown Course',
              code: courseData.code || record.course_id
            });
          } else {
            console.log(`Course document not found for ID: ${record.course_id}`);
            processedCourses.set(record.course_id, {
              name: 'Unknown Course',
              code: record.course_id
            });
          }
        } catch (error) {
          console.error(`Error fetching course data for ${record.course_id}:`, error);
          processedCourses.set(record.course_id, {
            name: 'Unknown Course',
            code: record.course_id
          });
        }
      }
    }
    
    // 6. Fetch level data - One by one
    for (const record of records) {
      if (record.level_id && !processedLevels.has(record.level_id)) {
        try {
          const levelDoc = await getDoc(doc(db, 'levels', record.level_id));
          if (levelDoc.exists()) {
            const levelData = levelDoc.data();
            processedLevels.set(record.level_id, {
              name: levelData.name || 'Unknown Level',
              code: levelData.code || record.level_id
            });
          } else {
            console.log(`Level document not found for ID: ${record.level_id}`);
            processedLevels.set(record.level_id, {
              name: 'Unknown Level',
              code: record.level_id
            });
          }
        } catch (error) {
          console.error(`Error fetching level data for ${record.level_id}:`, error);
          processedLevels.set(record.level_id, {
            name: 'Unknown Level',
            code: record.level_id
          });
        }
      }
    }
    
    // 7. Fill in the data for each record
    for (const record of records) {
      // Add student information
      if (record.student_id && processedStudents.has(record.student_id)) {
        const student = processedStudents.get(record.student_id);
        record.student_name = student.name;
        record.student_number = student.student_id;
      }
      
      // Add course information
      if (record.course_id && processedCourses.has(record.course_id)) {
        const course = processedCourses.get(record.course_id);
        record.course_name = course.name;
        record.course_code = course.code;
      }
      
      // Add level information
      if (record.level_id && processedLevels.has(record.level_id)) {
        const level = processedLevels.get(record.level_id);
        record.level_name = level.name;
        record.level_code = level.code;
      }
    }
    
    console.log(`Processed ${processedStudents.size} students, ${processedCourses.size} courses, ${processedLevels.size} levels`);
    
    // 8. Create Excel workbook
    const wb = XLSX.utils.book_new();
    
    // 9. Create attendance summary sheet
    const summaryData = [
      ['Attendance Export Summary'],
      [''],
      ['Date Range', `${formattedStartDate} to ${formattedEndDate}`],
      ['Total Records', `${records.length}`],
      [''],
      ['Status', 'Count', 'Percentage'],
    ];
    
    // Calculate statistics
    const statuses = ['present', 'absent', 'late', 'excused'];
    for (const status of statuses) {
      const count = records.filter(r => r.status === status).length;
      const percentage = records.length > 0 ? (count / records.length) * 100 : 0;
      summaryData.push([
        status.charAt(0).toUpperCase() + status.slice(1),
        count.toString(),
        `${percentage.toFixed(2)}%`
      ]);
    }
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, summarySheet, 'Summary');
    
    // Set column widths for summary sheet
    summarySheet['!cols'] = [
      { wch: 15 }, // Column A
      { wch: 15 }, // Column B
      { wch: 15 }, // Column C
    ];
    
    // 10. Create detailed attendance records sheet
    const headers = [
      'Date',
      'Student ID',
      'Student Name',
      'Course Code',
      'Course Name',
      'Level Code',
      'Level Name',
      'Status',
      'Notes'
    ];
    
    const rows = records.map(record => [
      record.date,
      record.student_number,
      record.student_name,
      record.course_code,
      record.course_name,
      record.level_code,
      record.level_name,
      record.status.charAt(0).toUpperCase() + record.status.slice(1), // Capitalize status
      record.notes
    ]);
    
    const detailsData = [headers, ...rows];
    const detailsSheet = XLSX.utils.aoa_to_sheet(detailsData);
    XLSX.utils.book_append_sheet(wb, detailsSheet, 'Attendance Records');
    
    // Set column widths for details sheet
    detailsSheet['!cols'] = [
      { wch: 12 }, // Date
      { wch: 12 }, // Student ID
      { wch: 25 }, // Student Name
      { wch: 12 }, // Course Code
      { wch: 25 }, // Course Name
      { wch: 12 }, // Level Code
      { wch: 25 }, // Level Name
      { wch: 10 }, // Status
      { wch: 30 }, // Notes
    ];
    
    // 11. Write to file and download
    console.log("Writing Excel file...");
    XLSX.writeFile(wb, `${title}.xlsx`);
    
    toast.success(`Attendance exported successfully for the ${range}`);
  } catch (error) {
    console.error('Error exporting attendance:', error);
    toast.error(`Failed to export attendance: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}; 