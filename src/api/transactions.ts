import * as transactionModel from '@/integrations/firebase/models/transaction';
import { auth } from '@/integrations/firebase/client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';
import { handleError } from '@/utils/error-handling';

export type TransactionInsert = {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  status: 'completed' | 'pending' | 'cancelled';
  notes?: string;
  payment_id?: string;
  student_id?: string;
};

export const getTransactions = async () => {
  try {
    console.log('Fetching transactions from Firebase');
    const transactions = await transactionModel.getAllTransactions();
    return transactions;
  } catch (error) {
    console.error('Error fetching transactions:', error);
    throw error;
  }
};

export const getTransactionsByDateRange = async (startDate: string, endDate: string) => {
  try {
    console.log(`Fetching transactions from ${startDate} to ${endDate}`);
    const transactions = await transactionModel.getTransactionsByDateRange(startDate, endDate);
    return transactions;
  } catch (error) {
    console.error('Error fetching transactions by date range:', error);
    throw error;
  }
};

export const getTransactionSummary = async () => {
  try {
    console.log('Calculating transaction summary');
    const summary = await transactionModel.calculateTransactionSummary();
    return summary;
  } catch (error) {
    console.error('Error calculating transaction summary:', error);
    throw error;
  }
};

export const createTransaction = async (transaction: TransactionInsert) => {
  try {
    console.log('Creating new transaction:', transaction);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const transactionId = await transactionModel.createTransaction(transaction);
    
    await logActivity('transaction_created', {
      transactionId,
      amount: transaction.amount,
      type: transaction.type
    });
    
    toast.success('Transaction recorded successfully');
    
    return {
      id: transactionId,
      ...transaction
    };
  } catch (error: any) {
    console.error('Error creating transaction:', error);
    toast.error(handleError(error, 'Failed to record transaction'));
    throw error;
  }
};

export const updateTransaction = async (id: string, transaction: Partial<TransactionInsert>) => {
  try {
    console.log('Updating transaction:', id, transaction);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    await transactionModel.updateTransaction(id, transaction);
    
    await logActivity('transaction_updated', {
      transactionId: id,
      updates: transaction
    });
    
    toast.success('Transaction updated successfully');
    
    return {
      id,
      ...transaction
    };
  } catch (error: any) {
    console.error('Error updating transaction:', error);
    toast.error(handleError(error, 'Failed to update transaction'));
    throw error;
  }
};

export const deleteTransaction = async (id: string) => {
  try {
    console.log('Deleting transaction:', id);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    await transactionModel.deleteTransaction(id);
    
    await logActivity('transaction_deleted', {
      transactionId: id
    });
    
    toast.success('Transaction deleted successfully');
    
    return { success: true };
  } catch (error: any) {
    console.error('Error deleting transaction:', error);
    toast.error(handleError(error, 'Failed to delete transaction'));
    throw error;
  }
};
