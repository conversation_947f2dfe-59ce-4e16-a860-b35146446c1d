import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as timetableModel from '@/integrations/firebase/models/timetable';
import type { 
  ClassTimetableEntry, 
  ExamTimetableEntry 
} from '@/integrations/firebase/models/timetable';

// Query keys
const CLASS_TIMETABLE_KEY = 'class-timetable';
const EXAM_TIMETABLE_KEY = 'exam-timetable';
const CLASS_TIMETABLE_BY_LEVEL_KEY = 'class-timetable-by-level';
const EXAM_TIMETABLE_BY_LEVEL_KEY = 'exam-timetable-by-level';
const CLASS_TIMETABLE_BY_DAY_KEY = 'class-timetable-by-day';
const CLASS_TIMETABLE_BY_TEACHER_KEY = 'class-timetable-by-teacher';
const EXAM_TIMETABLE_BY_DATE_RANGE_KEY = 'exam-timetable-by-date-range';
const EXAM_TIMETABLE_BY_TYPE_KEY = 'exam-timetable-by-type';

// Class Timetable Hooks

// Get all class timetable entries
export const useClassTimetable = () => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_KEY],
    queryFn: timetableModel.getAllClassTimetable,
  });
};

// Get class timetable entries by level
export const useClassTimetableByLevel = (levelId: string) => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_BY_LEVEL_KEY, levelId],
    queryFn: () => timetableModel.getClassTimetableByLevel(levelId),
    enabled: !!levelId,
  });
};

// Get class timetable entries by day
export const useClassTimetableByDay = (day: string) => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_BY_DAY_KEY, day],
    queryFn: () => timetableModel.getClassTimetableByDay(day),
    enabled: !!day,
  });
};

// Get class timetable entries by teacher
export const useClassTimetableByTeacher = (teacherId: string) => {
  return useQuery({
    queryKey: [CLASS_TIMETABLE_BY_TEACHER_KEY, teacherId],
    queryFn: () => timetableModel.getClassTimetableByTeacher(teacherId),
    enabled: !!teacherId,
  });
};

// Create a new class timetable entry
export const useCreateClassTimetableEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (entry: Omit<ClassTimetableEntry, 'id'>) => 
      timetableModel.createClassTimetableEntry(entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CLASS_TIMETABLE_KEY] });
    },
  });
};

// Update a class timetable entry
export const useUpdateClassTimetableEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, entry }: { id: string; entry: Partial<ClassTimetableEntry> }) => 
      timetableModel.updateClassTimetableEntry(id, entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CLASS_TIMETABLE_KEY] });
    },
  });
};

// Delete a class timetable entry
export const useDeleteClassTimetableEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => timetableModel.deleteClassTimetableEntry(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CLASS_TIMETABLE_KEY] });
    },
  });
};

// Exam Timetable Hooks

// Get all exam timetable entries
export const useExamTimetable = () => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_KEY],
    queryFn: timetableModel.getAllExamTimetable,
  });
};

// Get exam timetable entries by level
export const useExamTimetableByLevel = (levelId: string) => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_BY_LEVEL_KEY, levelId],
    queryFn: () => timetableModel.getExamTimetableByLevel(levelId),
    enabled: !!levelId,
  });
};

// Get exam timetable entries by date range
export const useExamTimetableByDateRange = (startDate: string, endDate: string) => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_BY_DATE_RANGE_KEY, startDate, endDate],
    queryFn: () => timetableModel.getExamTimetableByDateRange(startDate, endDate),
    enabled: !!startDate && !!endDate,
  });
};

// Get exam timetable entries by exam type
export const useExamTimetableByType = (examType: string) => {
  return useQuery({
    queryKey: [EXAM_TIMETABLE_BY_TYPE_KEY, examType],
    queryFn: () => timetableModel.getExamTimetableByType(examType),
    enabled: !!examType,
  });
};

// Create a new exam timetable entry
export const useCreateExamTimetableEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (entry: Omit<ExamTimetableEntry, 'id'>) => 
      timetableModel.createExamTimetableEntry(entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EXAM_TIMETABLE_KEY] });
    },
  });
};

// Update an exam timetable entry
export const useUpdateExamTimetableEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, entry }: { id: string; entry: Partial<ExamTimetableEntry> }) => 
      timetableModel.updateExamTimetableEntry(id, entry),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EXAM_TIMETABLE_KEY] });
    },
  });
};

// Delete an exam timetable entry
export const useDeleteExamTimetableEntry = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => timetableModel.deleteExamTimetableEntry(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EXAM_TIMETABLE_KEY] });
    },
  });
};

// Direct API functions for components that don't use React Query hooks

// Class Timetable Functions

// Get all class timetable entries
export const getAllClassTimetable = async (): Promise<ClassTimetableEntry[]> => {
  return await timetableModel.getAllClassTimetable();
};

// Get class timetable entries by level
export const getClassTimetableByLevel = async (levelId: string): Promise<ClassTimetableEntry[]> => {
  return await timetableModel.getClassTimetableByLevel(levelId);
};

// Get class timetable entries by day
export const getClassTimetableByDay = async (day: string): Promise<ClassTimetableEntry[]> => {
  return await timetableModel.getClassTimetableByDay(day);
};

// Get class timetable entries by teacher
export const getClassTimetableByTeacher = async (teacherId: string): Promise<ClassTimetableEntry[]> => {
  return await timetableModel.getClassTimetableByTeacher(teacherId);
};

// Create a new class timetable entry
export const createClassTimetableEntry = async (entry: Omit<ClassTimetableEntry, 'id'>): Promise<string> => {
  return await timetableModel.createClassTimetableEntry(entry);
};

// Update a class timetable entry
export const updateClassTimetableEntry = async (id: string, entry: Partial<ClassTimetableEntry>): Promise<void> => {
  await timetableModel.updateClassTimetableEntry(id, entry);
};

// Delete a class timetable entry
export const deleteClassTimetableEntry = async (id: string): Promise<void> => {
  await timetableModel.deleteClassTimetableEntry(id);
};

// Exam Timetable Functions

// Get all exam timetable entries
export const getAllExamTimetable = async (): Promise<ExamTimetableEntry[]> => {
  return await timetableModel.getAllExamTimetable();
};

// Get exam timetable entries by level
export const getExamTimetableByLevel = async (levelId: string): Promise<ExamTimetableEntry[]> => {
  return await timetableModel.getExamTimetableByLevel(levelId);
};

// Get exam timetable entries by date range
export const getExamTimetableByDateRange = async (startDate: string, endDate: string): Promise<ExamTimetableEntry[]> => {
  return await timetableModel.getExamTimetableByDateRange(startDate, endDate);
};

// Get exam timetable entries by exam type
export const getExamTimetableByType = async (examType: string): Promise<ExamTimetableEntry[]> => {
  return await timetableModel.getExamTimetableByType(examType);
};

// Create a new exam timetable entry
export const createExamTimetableEntry = async (entry: Omit<ExamTimetableEntry, 'id'>): Promise<string> => {
  return await timetableModel.createExamTimetableEntry(entry);
};

// Update an exam timetable entry
export const updateExamTimetableEntry = async (id: string, entry: Partial<ExamTimetableEntry>): Promise<void> => {
  await timetableModel.updateExamTimetableEntry(id, entry);
};

// Delete an exam timetable entry
export const deleteExamTimetableEntry = async (id: string): Promise<void> => {
  await timetableModel.deleteExamTimetableEntry(id);
}; 