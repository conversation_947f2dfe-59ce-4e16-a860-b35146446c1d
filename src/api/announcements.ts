import * as announcementModel from '@/integrations/firebase/models/announcement';
import { auth } from '@/integrations/firebase/client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export type AnnouncementFormData = {
  title: string;
  content: string;
  target_levels: string[];
  priority: string;
  expires_at: string;
  is_general?: boolean;
};

export const getAnnouncements = async () => {
  try {
    console.log('Fetching announcements from Firebase');
    const announcements = await announcementModel.getAllAnnouncements();
    
    // Sort announcements by creation date (newest first)
    return announcements.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw error;
  }
};

export const getAnnouncementById = async (id: string) => {
  try {
    console.log(`Fetching announcement with ID: ${id}`);
    const announcement = await announcementModel.getAnnouncementById(id);
    
    if (!announcement) {
      throw new Error('Announcement not found');
    }
    
    return announcement;
  } catch (error) {
    console.error('Error fetching announcement:', error);
    throw error;
  }
};

export const getAnnouncementsByLevel = async (levelId: string) => {
  try {
    console.log(`Fetching announcements for level: ${levelId}`);
    // Get announcements targeted at this level
    const levelAnnouncements = await announcementModel.getAnnouncementsByTargetLevel(levelId);
    
    // Get all general announcements
    const allAnnouncements = await announcementModel.getAllAnnouncements();
    const generalAnnouncements = allAnnouncements.filter(announcement => announcement.is_general === true);
    
    // Combine level-specific and general announcements
    const combinedAnnouncements = [...levelAnnouncements, ...generalAnnouncements];
    
    // Remove duplicates (in case a general announcement was also targeted at this level)
    const uniqueAnnouncements = Array.from(
      new Map(combinedAnnouncements.map(item => [item.id, item])).values()
    );
    
    // Sort announcements by creation date (newest first)
    return uniqueAnnouncements.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Error fetching announcements by level:', error);
    throw error;
  }
};

export const getActiveAnnouncements = async () => {
  try {
    console.log('Fetching active announcements');
    const now = new Date().toISOString();
    const announcements = await announcementModel.getAllAnnouncements();
    
    // Filter announcements that haven't expired yet
    const activeAnnouncements = announcements.filter(announcement => 
      new Date(announcement.expires_at).getTime() > new Date(now).getTime()
    );
    
    // Sort announcements by creation date (newest first)
    return activeAnnouncements.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Error fetching active announcements:', error);
    throw error;
  }
};

export const createAnnouncement = async (announcementData: AnnouncementFormData) => {
  try {
    console.log('Creating new announcement:', announcementData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // If it's a general announcement, we don't need target levels
    const finalData = {
      ...announcementData,
      // If it's a general announcement and target_levels is empty, set it to an empty array
      target_levels: announcementData.is_general ? [] : announcementData.target_levels,
      created_by: user.uid,
      created_at: new Date().toISOString(),
      author: user.displayName || 'Teacher'
    };
    
    const announcementId = await announcementModel.createAnnouncement(finalData);
    
    await logActivity('announcement_created', { 
      announcementId,
      title: announcementData.title,
      targetLevels: announcementData.target_levels,
      isGeneral: announcementData.is_general
    });
    
    toast.success('Announcement created successfully');
    
    return {
      id: announcementId,
      ...finalData
    };
  } catch (error: any) {
    console.error('Error creating announcement:', error);
    toast.error(error.message || 'Failed to create announcement');
    throw error;
  }
};

export const updateAnnouncement = async (id: string, announcementData: Partial<AnnouncementFormData>) => {
  try {
    console.log('Updating announcement:', id, announcementData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // If it's a general announcement, we don't need target levels
    const finalData = {
      ...announcementData,
      // If it's being updated to a general announcement, clear target_levels
      ...(announcementData.is_general ? { target_levels: [] } : {}),
      updated_by: user.uid,
      updated_at: new Date().toISOString()
    };
    
    await announcementModel.updateAnnouncement(id, finalData);
    
    await logActivity('announcement_updated', {
      announcementId: id,
      updates: announcementData,
      isGeneral: announcementData.is_general
    });
    
    toast.success('Announcement updated successfully');
    
    return {
      id,
      ...finalData
    };
  } catch (error: any) {
    console.error('Error updating announcement:', error);
    toast.error(error.message || 'Failed to update announcement');
    throw error;
  }
};

export const deleteAnnouncement = async (id: string) => {
  try {
    console.log('Deleting announcement:', id);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    await announcementModel.deleteAnnouncement(id);
    
    await logActivity('announcement_deleted', {
      announcementId: id
    });
    
    toast.success('Announcement deleted successfully');
    
    return { success: true };
  } catch (error: any) {
    console.error('Error deleting announcement:', error);
    toast.error(error.message || 'Failed to delete announcement');
    throw error;
  }
}; 