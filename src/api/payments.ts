import * as paymentModel from '@/integrations/firebase/models/payment';
import * as transactionModel from '@/integrations/firebase/models/transaction';
import * as studentModel from '@/integrations/firebase/models/student';
import * as courseModel from '@/integrations/firebase/models/course';
import { auth } from '@/integrations/firebase/client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

type PaymentInsert = {
  student_id: string;
  amount: number;
  date_paid: string;
  payment_due_date: string;
  status: 'paid' | 'pending' | 'overdue' | 'partial';
  notes?: string;
};

export const getPayments = async () => {
  try {
    console.log('Fetching payments from Firebase');
    const payments = await paymentModel.getAllPayments();
    
    // Fetch related student data for each payment
    const paymentsWithStudents = await Promise.all(
      payments.map(async (payment) => {
        let student = null;
        
        if (payment.student_id) {
          student = await studentModel.getStudentById(payment.student_id);
        }
        
        return {
          ...payment,
          student: student ? {
            id: student.id,
            name: student.name,
            student_id: student.student_id
          } : null
        };
      })
    );
    
    return paymentsWithStudents;
  } catch (error) {
    console.error('Error fetching payments:', error);
    throw error;
  }
};

export const getStudentPayments = async (studentId: string) => {
  try {
    console.log('Fetching student payments from Firebase');
    const payments = await paymentModel.getPaymentsByStudentId(studentId);
    return payments;
  } catch (error) {
    console.error('Error fetching student payments:', error);
    throw error;
  }
};

export const getStudentPaymentSummary = async (studentId: string) => {
  try {
    console.log('Calculating student payment summary for student ID:', studentId);
    const student = await studentModel.getStudentById(studentId);
    
    if (!student) {
      console.error('Student not found with ID:', studentId);
      throw new Error('Student not found');
    }
    
    // Get course fee from the student's associated course
    let courseFee = 0;
    if (student.course_id) {
      console.log('Fetching course fee for course ID:', student.course_id);
      try {
        const course = await courseModel.getCourseById(student.course_id);
        if (course) {
          courseFee = course.fee || 0;
          console.log('Course fee found:', courseFee);
        } else {
          console.warn('Course not found for ID:', student.course_id);
        }
      } catch (error) {
        console.error('Error fetching course:', error);
      }
    } else {
      console.warn('Student has no associated course ID');
    }
    
    // Get all payments for the student
    const payments = await paymentModel.getPaymentsByStudentId(studentId);
    
    // Calculate total paid amount directly
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
    
    // Calculate remaining balance
    const remainingBalance = courseFee - totalPaid;
    
    // Determine payment status based on actual payments vs course fee
    let paymentStatus: 'paid' | 'pending' | 'overdue' | 'partial';
    
    if (courseFee <= 0) {
      paymentStatus = 'pending'; // No course fee set
    } else if (remainingBalance <= 0) {
      paymentStatus = 'paid'; // Fully paid
    } else if (totalPaid === 0) {
      paymentStatus = 'pending'; // No payment made
    } else {
      paymentStatus = 'partial'; // Partial payment made
    }
    
    // Check if any payment is overdue
    const hasOverduePayments = payments.some(payment => {
      const dueDate = new Date(payment.payment_due_date);
      return dueDate < new Date() && payment.status === 'pending';
    });
    
    if (hasOverduePayments && paymentStatus !== 'paid') {
      paymentStatus = 'overdue';
    }
    
    // Update payment status in each payment document if needed
    if (paymentStatus === 'paid') {
      for (const payment of payments) {
        if (payment.status !== 'paid') {
          await paymentModel.updatePayment(payment.id, { status: 'paid' });
          console.log(`Updated payment ${payment.id} status to 'paid'`);
        }
      }
    }
    
    return {
      totalPaid,
      courseFee,
      remainingBalance,
      paymentStatus
    };
  } catch (error) {
    console.error('Error calculating student payment summary:', error);
    throw error;
  }
};

// Recalculate payment status for all students
export const recalculateAllStudentPaymentStatus = async () => {
  try {
    console.log('Recalculating payment status for all students');
    
    // Get all students
    const students = await studentModel.getAllStudents();
    console.log(`Found ${students.length} students`);
    
    let updatedCount = 0;
    
    // Process each student
    for (const student of students) {
      try {
        const summary = await getStudentPaymentSummary(student.id);
        
        // Update student payment status
        await studentModel.updateStudent(student.id, {
          payment_status: summary.paymentStatus
        });
        
        updatedCount++;
      } catch (error) {
        console.error(`Error updating student ${student.id}:`, error);
      }
    }
    
    console.log(`Successfully updated payment status for ${updatedCount} students`);
    return { success: true, updatedCount };
  } catch (error) {
    console.error('Error recalculating payment status:', error);
    throw error;
  }
};

export const createPayment = async (payment: PaymentInsert) => {
  try {
    console.log('Creating new payment:', payment);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    if (!payment.student_id) {
      throw new Error('Student ID is required');
    }
    
    // Find the student by student_id field instead of document ID
    const student = await studentModel.getStudentByStudentId(payment.student_id);
    
    if (!student) {
      throw new Error('Student not found with the provided Student ID');
    }

    // Create payment
    const paymentId = await paymentModel.createPayment({
      ...payment,
      student_id: student.id
    });
    
    // Map payment status to transaction status
    let transactionStatus: 'completed' | 'pending' | 'cancelled';
    if (payment.status === 'paid') {
      transactionStatus = 'completed';
    } else if (payment.status === 'pending' || payment.status === 'partial' || payment.status === 'overdue') {
      transactionStatus = 'pending';
    } else {
      transactionStatus = 'cancelled';
    }
    
    // Create corresponding income transaction
    const transactionId = await transactionModel.createTransaction({
      date: payment.date_paid,
      description: `Fee payment from student ${student.name} (ID: ${student.student_id})`,
      amount: payment.amount,
      type: 'income',
      category: 'tuition',
      status: transactionStatus,
      notes: payment.notes || `Payment recorded for student ${student.name}`,
      payment_id: paymentId,
      student_id: student.id
    });

    await logActivity('payment_created', { 
      paymentId, 
      amount: payment.amount,
      studentId: student.id,
      transactionId
    });
    
    toast.success('Payment recorded successfully');
    
    return {
      id: paymentId,
      ...payment
    };
  } catch (error: any) {
    console.error('Error creating payment:', error);
    toast.error(error.message || 'Failed to record payment');
    throw error;
  }
};
