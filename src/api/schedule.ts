import { db } from '@/integrations/firebase/client';
import { collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, doc } from 'firebase/firestore';
import { format } from 'date-fns';

export interface ClassSchedule {
  id: string;
  level_id: string;
  course_name: string;
  room: string;
  start_time: string;
  end_time: string;
  day_of_week: number; // 0-6 (Sunday-Saturday)
  teacher_id: string;
  created_at: string;
  updated_at: string;
}

export const getTeacherSchedule = async (teacherId: string): Promise<ClassSchedule[]> => {
  try {
    const scheduleRef = collection(db, 'schedules');
    const q = query(scheduleRef, where('teacher_id', '==', teacherId));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ClassSchedule));
  } catch (error) {
    console.error('Error fetching teacher schedule:', error);
    return [];
  }
};

export const createSchedule = async (scheduleData: Omit<ClassSchedule, 'id' | 'created_at' | 'updated_at'>): Promise<ClassSchedule | null> => {
  try {
    const now = new Date().toISOString();
    const scheduleRef = collection(db, 'schedules');
    const docRef = await addDoc(scheduleRef, {
      ...scheduleData,
      created_at: now,
      updated_at: now
    });

    return {
      id: docRef.id,
      ...scheduleData,
      created_at: now,
      updated_at: now
    };
  } catch (error) {
    console.error('Error creating schedule:', error);
    return null;
  }
};

export const updateSchedule = async (scheduleId: string, scheduleData: Partial<ClassSchedule>): Promise<boolean> => {
  try {
    const scheduleRef = doc(db, 'schedules', scheduleId);
    await updateDoc(scheduleRef, {
      ...scheduleData,
      updated_at: new Date().toISOString()
    });
    return true;
  } catch (error) {
    console.error('Error updating schedule:', error);
    return false;
  }
};

export const deleteSchedule = async (scheduleId: string): Promise<boolean> => {
  try {
    const scheduleRef = doc(db, 'schedules', scheduleId);
    await deleteDoc(scheduleRef);
    return true;
  } catch (error) {
    console.error('Error deleting schedule:', error);
    return false;
  }
};

export const getSchedulesByLevel = async (levelId: string): Promise<ClassSchedule[]> => {
  try {
    const scheduleRef = collection(db, 'schedules');
    const q = query(scheduleRef, where('level_id', '==', levelId));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as ClassSchedule));
  } catch (error) {
    console.error('Error fetching level schedules:', error);
    return [];
  }
};

export const checkScheduleConflicts = async (
  teacherId: string, 
  dayOfWeek: number, 
  startTime: string, 
  endTime: string,
  excludeScheduleId?: string
): Promise<boolean> => {
  try {
    const scheduleRef = collection(db, 'schedules');
    const q = query(scheduleRef, where('teacher_id', '==', teacherId), where('day_of_week', '==', dayOfWeek));
    const querySnapshot = await getDocs(q);
    
    const schedules = querySnapshot.docs
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ClassSchedule))
      .filter(schedule => schedule.id !== excludeScheduleId);

    // Check for time conflicts
    for (const schedule of schedules) {
      const newStart = parseInt(startTime.replace(':', ''));
      const newEnd = parseInt(endTime.replace(':', ''));
      const existingStart = parseInt(schedule.start_time.replace(':', ''));
      const existingEnd = parseInt(schedule.end_time.replace(':', ''));

      if (
        (newStart >= existingStart && newStart < existingEnd) ||
        (newEnd > existingStart && newEnd <= existingEnd) ||
        (newStart <= existingStart && newEnd >= existingEnd)
      ) {
        return true; // Conflict found
      }
    }

    return false; // No conflicts
  } catch (error) {
    console.error('Error checking schedule conflicts:', error);
    return true; // Return true to prevent scheduling in case of error
  }
}; 