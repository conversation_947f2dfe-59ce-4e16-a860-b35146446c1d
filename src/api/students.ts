import type { Student as StudentType } from '@/types/student';
import { toast } from 'sonner';
import * as studentModel from '@/integrations/firebase/models/student';
import { db } from '@/integrations/firebase/client';
import { collection, doc, getDoc } from 'firebase/firestore';

export const getStudents = async () => {
  try {
    const students = await studentModel.getAllStudents();
    
    // Fetch related course and level data for each student
    const studentsWithRelations = await Promise.all(
      students.map(async (student) => {
        let course = null;
        let level = null;
        
        if (student.course_id) {
          const courseDoc = await getDoc(doc(db, 'courses', student.course_id));
          if (courseDoc.exists()) {
            course = { id: courseDoc.id, ...courseDoc.data() };
          }
        }
        
        if (student.level_id) {
          const levelDoc = await getDoc(doc(db, 'levels', student.level_id));
          if (levelDoc.exists()) {
            level = { id: levelDoc.id, ...levelDoc.data() };
          }
        }
        
        return {
          ...student,
          course,
          level
        };
      })
    );
    
    return studentsWithRelations;
  } catch (error) {
    console.error('Error fetching students:', error);
    throw error;
  }
};

export const getStudentsByLevel = async (levelId: string) => {
  try {
    console.log('Fetching students for level:', levelId);
    
    // Get all students with the specified level_id
    const students = await studentModel.getAllStudents({ level_id: levelId });
    
    // Fetch related course data for each student
    const studentsWithCourses = await Promise.all(
      students.map(async (student) => {
        let course = null;
        
        if (student.course_id) {
          const courseDoc = await getDoc(doc(db, 'courses', student.course_id));
          if (courseDoc.exists()) {
            course = { id: courseDoc.id, ...courseDoc.data() };
          }
        }
        
        return {
          ...student,
          course
        };
      })
    );
    
    console.log(`Found ${studentsWithCourses.length} students in level ${levelId}`);
    return studentsWithCourses;
  } catch (error) {
    console.error('Error fetching students by level:', error);
    throw error;
  }
};

export const getStudent = async (id: string) => {
  try {
    const student = await studentModel.getStudentById(id);
    
    if (!student) {
      throw new Error('Student not found');
    }
    
    // Fetch related course and level data
    let course = null;
    let level = null;
    
    if (student.course_id) {
      const courseDoc = await getDoc(doc(db, 'courses', student.course_id));
      if (courseDoc.exists()) {
        course = { id: courseDoc.id, ...courseDoc.data() };
      }
    }
    
    if (student.level_id) {
      const levelDoc = await getDoc(doc(db, 'levels', student.level_id));
      if (levelDoc.exists()) {
        level = { id: levelDoc.id, ...levelDoc.data() };
      }
    }
    
    return {
      ...student,
      course,
      level
    };
  } catch (error) {
    console.error('Error fetching student:', error);
    throw error;
  }
};

export const updateStudent = async (id: string, data: Partial<StudentType>) => {
  try {
    const mappedData: Record<string, any> = {
      name: data.name,
      student_id: data.student_id,
      date_of_birth: data.date_of_birth,
      gender: data.gender,
      address: data.address,
      mobile_number: data.mobile_number,
      whatsapp_number: data.whatsapp_number,
      parent_name: data.parent_name,
      parent_mobile: data.parent_mobile,
      parent_whatsapp: data.parent_whatsapp,
      parent_email: data.parent_email,
      parent_occupation: data.parent_occupation,
      passport_picture: data.passport_picture,
      course_id: data.course_id,
      level_id: data.level_id,
      enrollment_status: data.enrollment_status,
      nationality: data.nationality,
      payment_status: data.payment_status
    };

    // Remove undefined fields
    Object.keys(mappedData).forEach(key => {
      if (mappedData[key] === undefined) {
        delete mappedData[key];
      }
    });

    await studentModel.updateStudent(id, mappedData);

    toast.success('Student updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating student:', error);
    toast.error(error.message || 'Failed to update student');
    throw error;
  }
};

export const deleteStudent = async (id: string) => {
  try {
    // Get student details first
    const student = await studentModel.getStudentById(id);

    if (!student) {
      toast.error('Student not found');
      return false;
    }

    // Delete student record
    await studentModel.deleteStudent(id);

    toast.success(`Successfully deleted student ${student.name} (${student.student_id})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting student:', error);
    toast.error(error.message || 'Failed to delete student');
    throw error;
  }
};

export const createStudent = async (data: Omit<StudentType, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const mappedData = {
      name: data.name,
      student_id: data.student_id,
      date_of_birth: data.date_of_birth,
      date_of_registration: data.date_of_registration || new Date().toISOString(),
      gender: data.gender,
      address: data.address,
      mobile_number: data.mobile_number,
      whatsapp_number: data.whatsapp_number,
      parent_name: data.parent_name,
      parent_mobile: data.parent_mobile,
      parent_whatsapp: data.parent_whatsapp,
      parent_email: data.parent_email,
      parent_occupation: data.parent_occupation,
      passport_picture: data.passport_picture,
      course_id: data.course_id,
      level_id: data.level_id,
      enrollment_status: data.enrollment_status || 'Active',
      nationality: data.nationality || '',
      payment_status: data.payment_status || 'Pending'
    };

    const newStudentId = await studentModel.createStudent(mappedData);
    const newStudent = await studentModel.getStudentById(newStudentId);

    toast.success('Student created successfully');
    return newStudent;
  } catch (error: any) {
    console.error('Error creating student:', error);
    toast.error(error.message || 'Failed to create student');
    throw error;
  }
};

export const searchStudents = async (query: string) => {
  try {
    console.log('Searching students with query:', query);
    
    // Get all students first
    const allStudents = await studentModel.getAllStudents({ enrollment_status: 'Active' });
    
    // If no query, return all active students
    if (!query || query.trim() === '') {
      return allStudents;
    }
    
    // Filter students based on query matching name or student_id
    const lowercaseQuery = query.toLowerCase();
    const filteredStudents = allStudents.filter(student => 
      student.name?.toLowerCase().includes(lowercaseQuery) || 
      student.student_id?.toLowerCase().includes(lowercaseQuery)
    );
    
    console.log(`Found ${filteredStudents.length} students matching query`);
    return filteredStudents;
  } catch (error) {
    console.error('Error searching students:', error);
    throw error;
  }
};

// ID Card related functions
export const updateIdCardStatus = async (studentId: string, status: string, expiryDate?: string) => {
  try {
    const updateData: Record<string, any> = {
      id_card_status: status
    };
    
    if (expiryDate) {
      updateData.id_card_expiry_date = expiryDate;
    }
    
    await studentModel.updateStudent(studentId, updateData);
    return true;
  } catch (error: any) {
    console.error('Error updating ID card status:', error);
    throw error;
  }
};

export const checkAndUpdateExpiredIdCards = async () => {
  try {
    const allStudents = await studentModel.getAllStudents();
    const currentDate = new Date();
    
    const updates = allStudents
      .filter(student => {
        if (!student.id_card_expiry_date || student.id_card_status !== 'generated') {
          return false;
        }
        
        const expiryDate = new Date(student.id_card_expiry_date);
        return expiryDate < currentDate;
      })
      .map(student => updateIdCardStatus(student.id, 'expired'));
    
    await Promise.all(updates);
    return updates.length;
  } catch (error: any) {
    console.error('Error checking and updating expired ID cards:', error);
    throw error;
  }
};
