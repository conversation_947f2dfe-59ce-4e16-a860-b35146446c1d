import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';

// Event type definition
export interface Event {
  id: string;
  title: string;
  description?: string;
  date: string;
  time?: string;
  category: string;
  location?: string;
  created_at?: string;
  updated_at?: string;
}

// Query keys
const EVENTS_KEY = 'events';
const EVENT_DETAIL_KEY = 'event-detail';
const EVENTS_BY_CATEGORY_KEY = 'events-by-category';
const EVENTS_BY_DATE_RANGE_KEY = 'events-by-date-range';

// Get all events
export const useEvents = () => {
  return useQuery({
    queryKey: [EVENTS_KEY],
    queryFn: eventModel.getEvents,
  });
};

// Get a single event by ID
export const useEvent = (id: string) => {
  return useQuery({
    queryKey: [EVENT_DETAIL_KEY, id],
    queryFn: () => eventModel.getEvent(id),
    enabled: !!id,
  });
};

// Get events by category
export const useEventsByCategory = (category: string) => {
  return useQuery({
    queryKey: [EVENTS_BY_CATEGORY_KEY, category],
    queryFn: () => eventModel.getEventsByCategory(category),
    enabled: !!category,
  });
};

// Get events by date range
export const useEventsByDateRange = (startDate: string, endDate: string) => {
  return useQuery({
    queryKey: [EVENTS_BY_DATE_RANGE_KEY, startDate, endDate],
    queryFn: () => eventModel.getEventsByDateRange(startDate, endDate),
    enabled: !!startDate && !!endDate,
  });
};

// Create a new event
export const useCreateEvent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (event: Omit<Event, 'id'>) => eventModel.createEvent(event),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EVENTS_KEY] });
    },
  });
};

// Update an event
export const useUpdateEvent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, event }: { id: string; event: Partial<Event> }) => 
      eventModel.updateEvent(id, event),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: [EVENTS_KEY] });
      queryClient.invalidateQueries({ queryKey: [EVENT_DETAIL_KEY, id] });
    },
  });
};

// Delete an event
export const useDeleteEvent = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => eventModel.deleteEvent(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [EVENTS_KEY] });
    },
  });
};

// Direct API functions for components that don't use React Query hooks

// Get all events
export const getEvents = async (): Promise<Event[]> => {
  return await eventModel.getEvents();
};

// Get a single event by ID
export const getEvent = async (id: string): Promise<Event | null> => {
  return await eventModel.getEvent(id);
};

// Get events by category
export const getEventsByCategory = async (category: string): Promise<Event[]> => {
  return await eventModel.getEventsByCategory(category);
};

// Get events by date range
export const getEventsByDateRange = async (startDate: string, endDate: string): Promise<Event[]> => {
  return await eventModel.getEventsByDateRange(startDate, endDate);
};

// Create a new event
export const createEvent = async (event: Omit<Event, 'id'>): Promise<string> => {
  return await eventModel.createEvent(event);
};

// Update an event
export const updateEvent = async (id: string, event: Partial<Event>): Promise<void> => {
  await eventModel.updateEvent(id, event);
};

// Delete an event
export const deleteEvent = async (id: string): Promise<void> => {
  await eventModel.deleteEvent(id);
}; 