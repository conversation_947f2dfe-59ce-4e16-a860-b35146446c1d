import * as levelModel from '@/integrations/firebase/models/level';
import * as courseModel from '@/integrations/firebase/models/course';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export const getLevels = async () => {
  try {
    console.log('Fetching levels from Firestore');
    const levels = await levelModel.getAllLevels();
    
    // Fetch related course data for each level
    const levelsWithCourses = await Promise.all(
      levels.map(async (level) => {
        let course = null;
        
        if (level.course_id) {
          course = await courseModel.getCourseById(level.course_id);
        }
        
        return {
          ...level,
          course: course ? { name: course.name } : null
        };
      })
    );
    
    return levelsWithCourses;
  } catch (error) {
    console.error('Error fetching levels:', error);
    throw error;
  }
};

export const createLevel = async (data: Omit<levelModel.Level, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    console.log('Creating new level:', data);
    const levelId = await levelModel.createLevel(data);
    const newLevel = await levelModel.getLevelById(levelId);
    
    if (newLevel) {
      await logActivity('level_created', { levelId: newLevel.id });
    }
    
    toast.success('Level created successfully');
    return newLevel;
  } catch (error: any) {
    console.error('Error creating level:', error);
    toast.error(error.message || 'Failed to create level');
    throw error;
  }
};

export const updateLevel = async (id: string, data: Partial<levelModel.Level>) => {
  try {
    await levelModel.updateLevel(id, data);
    
    await logActivity('level_updated', { levelId: id });
    
    toast.success('Level updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating level:', error);
    toast.error(error.message || 'Failed to update level');
    throw error;
  }
};

export const deleteLevel = async (id: string) => {
  try {
    const level = await levelModel.getLevelById(id);
    
    if (!level) {
      toast.error('Level not found');
      return false;
    }
    
    await levelModel.deleteLevel(id);
    
    await logActivity('level_deleted', { levelId: id });
    
    toast.success(`Successfully deleted level ${level.name} (${level.code})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting level:', error);
    toast.error(error.message || 'Failed to delete level');
    throw error;
  }
};
