import * as courseModel from '@/integrations/firebase/models/course';
import { toast } from 'sonner';

export const getCourses = async () => {
  try {
    const courses = await courseModel.getAllCourses();
    return courses;
  } catch (error) {
    console.error('Error fetching courses:', error);
    throw error;
  }
};

export const getCourse = async (id: string) => {
  try {
    const course = await courseModel.getCourseById(id);
    
    if (!course) {
      throw new Error('Course not found');
    }
    
    return course;
  } catch (error) {
    console.error('Error fetching course:', error);
    throw error;
  }
};

export const createCourse = async (data: Omit<courseModel.Course, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const courseId = await courseModel.createCourse(data);
    const newCourse = await courseModel.getCourseById(courseId);
    
    toast.success('Course created successfully');
    return newCourse;
  } catch (error: any) {
    console.error('Error creating course:', error);
    toast.error(error.message || 'Failed to create course');
    throw error;
  }
};

export const updateCourse = async (id: string, data: Partial<courseModel.Course>) => {
  try {
    await courseModel.updateCourse(id, data);
    
    toast.success('Course updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating course:', error);
    toast.error(error.message || 'Failed to update course');
    throw error;
  }
};

export const deleteCourse = async (id: string) => {
  try {
    const course = await courseModel.getCourseById(id);
    
    if (!course) {
      toast.error('Course not found');
      return false;
    }
    
    await courseModel.deleteCourse(id);
    
    toast.success(`Successfully deleted course ${course.name} (${course.code})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting course:', error);
    toast.error(error.message || 'Failed to delete course');
    throw error;
  }
};
