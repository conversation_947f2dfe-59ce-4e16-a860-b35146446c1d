import * as examResultModel from '@/integrations/firebase/models/exam-result';
import * as studentModel from '@/integrations/firebase/models/student';
import * as attendanceModel from '@/integrations/firebase/models/attendance';
import * as assignmentModel from '@/integrations/firebase/models/assignment';
import * as levelModel from '@/integrations/firebase/models/level';
import { auth } from '@/integrations/firebase/client';

export interface PerformanceData {
  averageScore: number;
  highestScore: number;
  lowestScore: number;
  passRate: number;
  improvementRate: number;
  students: StudentPerformance[];
}

export interface StudentPerformance {
  id: string;
  name: string;
  averageScore: number;
  attendance: number;
  assignments: number;
  exams: number;
  trend: 'up' | 'down' | 'stable';
}

export const getPerformanceByLevel = async (levelId: string): Promise<PerformanceData> => {
  try {
    console.log(`Fetching performance data for level: ${levelId}`);
    
    // Get all students in the level
    const students = await studentModel.getAllStudents({ level_id: levelId });
    
    if (students.length === 0) {
      return {
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        passRate: 0,
        improvementRate: 0,
        students: []
      };
    }
    
    // Get all exam results for students in this level
    const studentIds = students.map(student => student.id);
    const examResults = await Promise.all(
      studentIds.map(studentId => examResultModel.getExamResultsByStudentId(studentId))
    );
    
    // Flatten the results
    const allExamResults = examResults.flat();
    
    // Get attendance records for the past month
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    const startDate = oneMonthAgo.toISOString().split('T')[0];
    const endDate = new Date().toISOString().split('T')[0];
    
    const attendanceRecords = await attendanceModel.getAttendanceRecordsByDateRange(startDate, endDate);
    
    // Calculate performance metrics for each student
    const studentPerformances: StudentPerformance[] = await Promise.all(
      students.map(async (student) => {
        // Get exam results for this student
        const studentExamResults = allExamResults.filter(result => result.student_id === student.id);
        
        // Calculate average exam score
        const examScores = studentExamResults.map(result => result.marks);
        const averageExamScore = examScores.length > 0 
          ? examScores.reduce((sum, score) => sum + score, 0) / examScores.length 
          : 0;
        
        // Calculate attendance rate
        const studentAttendance = attendanceRecords.filter(record => 
          record.student_id === student.id && record.level_id === levelId
        );
        
        const attendanceRate = studentAttendance.length > 0
          ? (studentAttendance.filter(record => record.status === 'present').length / studentAttendance.length) * 100
          : 0;
        
        // For assignments, we'll just use a placeholder for now
        // In a real implementation, you would calculate this based on assignment submissions
        const assignmentScore = 85; // Placeholder
        
        // Determine trend (simplified)
        // In a real implementation, you would compare with previous period
        const trend: 'up' | 'down' | 'stable' = averageExamScore > 75 ? 'up' : 'down';
        
        return {
          id: student.id,
          name: student.name,
          averageScore: Math.round(averageExamScore),
          attendance: Math.round(attendanceRate),
          assignments: assignmentScore,
          exams: Math.round(averageExamScore),
          trend
        };
      })
    );
    
    // Calculate overall metrics
    const allScores = studentPerformances.map(perf => perf.averageScore);
    const averageScore = allScores.length > 0 
      ? Math.round(allScores.reduce((sum, score) => sum + score, 0) / allScores.length)
      : 0;
    
    const highestScore = allScores.length > 0 ? Math.max(...allScores) : 0;
    const lowestScore = allScores.length > 0 ? Math.min(...allScores) : 0;
    
    // Calculate pass rate (students with average score >= 60)
    const passingStudents = studentPerformances.filter(perf => perf.averageScore >= 60);
    const passRate = studentPerformances.length > 0 
      ? Math.round((passingStudents.length / studentPerformances.length) * 100)
      : 0;
    
    // Improvement rate (placeholder)
    // In a real implementation, you would compare with previous period
    const improvementRate = 15; // Placeholder
    
    return {
      averageScore,
      highestScore,
      lowestScore,
      passRate,
      improvementRate,
      students: studentPerformances.sort((a, b) => b.averageScore - a.averageScore)
    };
  } catch (error) {
    console.error('Error fetching performance data:', error);
    throw error;
  }
};

export const getStudentPerformance = async (studentId: string): Promise<StudentPerformance | null> => {
  try {
    console.log(`Fetching performance data for student: ${studentId}`);
    
    // Get student details
    const student = await studentModel.getStudentById(studentId);
    
    if (!student) {
      throw new Error('Student not found');
    }
    
    // Get exam results for this student
    const examResults = await examResultModel.getExamResultsByStudentId(studentId);
    
    // Calculate average exam score
    const examScores = examResults.map(result => result.marks);
    const averageExamScore = examScores.length > 0 
      ? examScores.reduce((sum, score) => sum + score, 0) / examScores.length 
      : 0;
    
    // Get attendance records for the past month
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    const startDate = oneMonthAgo.toISOString().split('T')[0];
    const endDate = new Date().toISOString().split('T')[0];
    
    const attendanceRecords = await attendanceModel.getAttendanceRecordsByStudentId(studentId);
    const recentAttendance = attendanceRecords.filter(record => 
      record.date >= startDate && record.date <= endDate
    );
    
    // Calculate attendance rate
    const attendanceRate = recentAttendance.length > 0
      ? (recentAttendance.filter(record => record.status === 'present').length / recentAttendance.length) * 100
      : 0;
    
    // For assignments, we'll just use a placeholder for now
    // In a real implementation, you would calculate this based on assignment submissions
    const assignmentScore = 85; // Placeholder
    
    // Determine trend (simplified)
    // In a real implementation, you would compare with previous period
    const trend: 'up' | 'down' | 'stable' = averageExamScore > 75 ? 'up' : 'down';
    
    return {
      id: student.id,
      name: student.name,
      averageScore: Math.round(averageExamScore),
      attendance: Math.round(attendanceRate),
      assignments: assignmentScore,
      exams: Math.round(averageExamScore),
      trend
    };
  } catch (error) {
    console.error('Error fetching student performance:', error);
    throw error;
  }
}; 