import { supabase } from '@/integrations/supabase/client';
import type { User } from '@/types/user';
import { createWithId, update, remove, getAll, getById, whereEqual } from '@/integrations/firebase/firestore';
import type { ProfileDocument } from '@/integrations/firebase/firestore';
import { deleteUserAccount } from '@/integrations/firebase/auth';
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut as firebaseSignOut, updateProfile as updateFirebaseProfile } from 'firebase/auth';
import { auth } from '@/integrations/firebase/client';

export const getUsers = async (): Promise<User[]> => {
  try {
    console.log('Fetching users from Firebase');
    const profiles = await getAll<ProfileDocument>('profiles');
    console.log(`Fetched ${profiles.length} profiles from Firebase`);
    
    return profiles.map(profile => ({
      id: profile.id,
      name: profile.displayName,
      email: profile.email,
      role: profile.role as 'admin' | 'teacher' | 'student' | 'parent',
      created_at: profile.created_at,
      updated_at: profile.updated_at
    }));
  } catch (error) {
    console.error('Failed to fetch users from Firebase:', error);
    return [];
  }
};

export const createUser = async ({ 
  name, 
  email, 
  password, 
  role = 'student' 
}: { 
  name: string; 
  email: string; 
  password: string; 
  role?: 'admin' | 'teacher' | 'student' | 'parent'; 
}): Promise<User> => {
  try {
    console.log(`Creating user in Firebase: ${name} (${email}) with role ${role}`);
    
    // Create the user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;
    
    if (!firebaseUser) {
      throw new Error('Failed to create Firebase auth user');
    }
    
    console.log(`Created Firebase auth user with ID: ${firebaseUser.uid}`);
    
    // Update the user's display name
    await updateFirebaseProfile(firebaseUser, { displayName: name });
    
    // Create the user profile in Firestore
    const profileData: Omit<ProfileDocument, 'id'> = {
      email,
      displayName: name,
      role,
      photoURL: ''
    };
    
    await createWithId<Omit<ProfileDocument, 'id'>>('profiles', firebaseUser.uid, profileData);
    console.log(`Created user profile in Firestore with ID: ${firebaseUser.uid}`);
    
    // Verify the profile was created
    const createdProfile = await getById<ProfileDocument>('profiles', firebaseUser.uid);
    if (!createdProfile) {
      console.error('Profile was not found after creation');
      // Continue anyway since the auth user was created
    } else {
      console.log('Successfully verified profile creation');
    }
    
    // Return the user object
    return {
      id: firebaseUser.uid,
      name,
      email,
      role,
      created_at: new Date(),
    };
  } catch (error: any) {
    console.error('Error creating user in Firebase:', error);
    
    // Handle specific Firebase auth errors
    if (error.code === 'auth/email-already-in-use') {
      throw new Error('auth/email-already-in-use');
    }
    
    throw error;
  }
};

export const updateUserRole = async (userId: string, role: 'admin' | 'teacher' | 'student' | 'parent'): Promise<boolean> => {
  try {
    await update<Partial<ProfileDocument>>('profiles', userId, { role });
    return true;
  } catch (error) {
    console.error('Error updating user role in Firebase:', error);
    throw error;
  }
};

export const deleteUser = async (id: string): Promise<boolean> => {
  try {
    await remove('profiles', id);
    // Note: This doesn't delete the auth user, only the profile
    // To fully delete a user, you would need admin SDK or a Cloud Function
    return true;
  } catch (error) {
    console.error('Error deleting user from Firebase:', error);
    throw error;
  }
};
