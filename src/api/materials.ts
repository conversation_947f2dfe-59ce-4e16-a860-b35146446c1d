import * as materialModel from '@/integrations/firebase/models/material';
import { auth } from '@/integrations/firebase/client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';
import { uploadFile, deleteFile, FOLDERS } from '@/integrations/firebase/storage';

export type MaterialFormData = {
  title: string;
  description: string;
  level_id: string;
  type: string;
  file?: File;
};

export const getMaterials = async () => {
  try {
    return await materialModel.getMaterials();
  } catch (error: any) {
    console.error('Error fetching materials:', error);
    toast.error(error.message || 'Failed to fetch materials');
    throw error;
  }
};

export const getMaterialsByLevel = async (levelId: string) => {
  try {
    return await materialModel.getMaterialsByLevel(levelId);
  } catch (error: any) {
    console.error('Error fetching materials by level:', error);
    toast.error(error.message || 'Failed to fetch materials');
    throw error;
  }
};

export const getMaterialById = async (id: string) => {
  try {
    return await materialModel.getMaterialById(id);
  } catch (error: any) {
    console.error('Error fetching material:', error);
    toast.error(error.message || 'Failed to fetch material');
    throw error;
  }
};

export const getMaterialsByType = async (type: string) => {
  try {
    console.log(`Fetching materials of type: ${type}`);
    const materials = await materialModel.getMaterialsByType(type);
    
    // Sort materials by creation date (newest first)
    return materials.sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime());
  } catch (error) {
    console.error('Error fetching materials by type:', error);
    throw error;
  }
};

export const createMaterial = async (materialData: MaterialFormData) => {
  try {
    console.log('Creating new material:', materialData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    let fileUrl = '';
    let fileName = '';
    let fileSize = 0;
    let fileId = '';
    let filePath = '';
    
    // Upload file if provided
    if (materialData.file) {
      const file = materialData.file;
      fileName = `${Date.now()}_${file.name}`;
      fileSize = file.size;
      
      // Upload file to Firebase Storage
      const folderPath = FOLDERS.MATERIALS;
      const uploadedFile = await uploadFile(file, fileName, folderPath, file.type);
      
      // Get the file URL and ID
      fileUrl = uploadedFile.downloadUrl;
      fileId = uploadedFile.id;
      filePath = uploadedFile.fullPath;
    }
    
    // Create the material record in Firestore
    const materialId = await materialModel.createMaterial({
      title: materialData.title,
      description: materialData.description,
      level_id: materialData.level_id,
      type: materialData.type,
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize,
      file_id: fileId,
      file_path: filePath, // Store full path for later deletion
      created_by: user.uid,
      created_at: new Date().toISOString()
    });
    
    await logActivity('material_created', { 
      materialId,
      title: materialData.title,
      levelId: materialData.level_id,
      type: materialData.type
    });
    
    toast.success('Material uploaded successfully');
    
    return {
      id: materialId,
      title: materialData.title,
      description: materialData.description,
      level_id: materialData.level_id,
      type: materialData.type,
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize,
      file_id: fileId,
      file_path: filePath,
      created_by: user.uid,
      created_at: new Date().toISOString()
    };
  } catch (error: any) {
    console.error('Error creating material:', error);
    toast.error(error.message || 'Failed to upload material');
    throw error;
  }
};

export const updateMaterial = async (id: string, materialData: Partial<MaterialFormData>) => {
  try {
    console.log('Updating material:', id, materialData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Get the existing material
    const existingMaterial = await materialModel.getMaterialById(id);
    if (!existingMaterial) {
      throw new Error('Material not found');
    }
    
    let fileUrl = existingMaterial.file_url || '';
    let fileName = existingMaterial.file_name || '';
    let fileSize = existingMaterial.file_size || 0;
    let fileId = existingMaterial.file_id || '';
    let filePath = existingMaterial.file_path || '';
    
    // Upload new file if provided
    if (materialData.file) {
      const file = materialData.file;
      fileName = `${Date.now()}_${file.name}`;
      fileSize = file.size;
      
      // Delete the old file if it exists
      if (existingMaterial.file_path) {
        try {
          await deleteFile(existingMaterial.file_path);
        } catch (error) {
          console.warn('Error deleting old file:', error);
          // Continue even if old file deletion fails
        }
      }
      
      // Upload new file to Firebase Storage
      const folderPath = FOLDERS.MATERIALS;
      const uploadedFile = await uploadFile(file, fileName, folderPath, file.type);
      
      // Get the file URL and ID
      fileUrl = uploadedFile.downloadUrl;
      fileId = uploadedFile.id;
      filePath = uploadedFile.fullPath;
    }
    
    // Update the material record in Firestore
    const updateData: any = {
      ...materialData,
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize,
      file_id: fileId,
      file_path: filePath,
      updated_by: user.uid,
      updated_at: new Date().toISOString()
    };
    
    // Remove the file property as it's not stored in Firestore
    delete updateData.file;
    
    await materialModel.updateMaterial(id, updateData);
    
    await logActivity('material_updated', {
      materialId: id,
      title: materialData.title || existingMaterial.title
    });
    
    toast.success('Material updated successfully');
    
    return {
      id,
      ...existingMaterial,
      ...updateData,
    };
  } catch (error: any) {
    console.error('Error updating material:', error);
    toast.error(error.message || 'Failed to update material');
    throw error;
  }
};

export const deleteMaterial = async (id: string) => {
  try {
    console.log('Deleting material:', id);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Get the material to delete
    const material = await materialModel.getMaterialById(id);
    if (!material) {
      throw new Error('Material not found');
    }
    
    // Delete the file from Firebase Storage if it exists
    if (material.file_path) {
      try {
        await deleteFile(material.file_path);
      } catch (error) {
        console.warn('Error deleting file from Firebase Storage:', error);
        // Continue even if file deletion fails
      }
    }
    
    // Delete the material record from Firestore
    await materialModel.deleteMaterial(id);
    
    await logActivity('material_deleted', {
      materialId: id,
      title: material.title
    });
    
    toast.success('Material deleted successfully');
    
    return { success: true };
  } catch (error: any) {
    console.error('Error deleting material:', error);
    toast.error(error.message || 'Failed to delete material');
    throw error;
  }
}; 