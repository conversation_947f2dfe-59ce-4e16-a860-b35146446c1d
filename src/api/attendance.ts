import * as attendanceModel from '@/integrations/firebase/models/attendance';
import * as studentModel from '@/integrations/firebase/models/student';
import { auth } from '@/integrations/firebase/client';
import { toast } from 'sonner';
import { logActivity } from '@/utils/activity-logger';

// For single date attendance records
export const getAttendanceByDate = async (levelId: string, date: string) => {
  try {
    // Get attendance records for the specified date
    const records = await attendanceModel.getAttendanceRecordsByDate(date);
    
    // Filter by level
    const filteredRecords = records.filter(record => record.level_id === levelId);
    
    // Fetch student details for each record
    const recordsWithStudents = await Promise.all(
      filteredRecords.map(async (record) => {
        const student = await studentModel.getStudentById(record.student_id);
        return {
          ...record,
          student_name: student?.name,
          student
        };
      })
    );
    
    return recordsWithStudents;
  } catch (error) {
    console.error('Error fetching attendance records by date:', error);
    throw error;
  }
};

// For date range attendance records
export const getAttendanceByDateRange = async (levelId: string, startDate: string, endDate: string) => {
  try {
    // Get attendance records for the date range
    const records = await attendanceModel.getAttendanceRecordsByDateRange(startDate, endDate);
    
    // Filter by level
    const filteredRecords = records.filter(record => record.level_id === levelId);
    
    // Fetch student details for each record
    const recordsWithStudents = await Promise.all(
      filteredRecords.map(async (record) => {
        const student = await studentModel.getStudentById(record.student_id);
        return {
          ...record,
          student_name: student?.name,
          student
        };
      })
    );
    
    return recordsWithStudents;
  } catch (error) {
    console.error('Error fetching attendance records by date range:', error);
    throw error;
  }
};

export const getAttendanceRecords = async (date?: string) => {
  try {
    let records;
    
    if (date) {
      records = await attendanceModel.getAttendanceRecordsByDate(date);
    } else {
      records = await attendanceModel.getAllAttendanceRecords();
    }
    
    // Fetch student details for each record
    const recordsWithStudents = await Promise.all(
      records.map(async (record) => {
        const student = await studentModel.getStudentById(record.student_id);
        return {
          ...record,
          student
        };
      })
    );
    
    return recordsWithStudents;
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    throw error;
  }
};

export const getStudentAttendance = async (studentId: string) => {
  try {
    console.log('Fetching student attendance from Firebase for student ID:', studentId);
    
    // Get all attendance records for this student
    const records = await attendanceModel.getAttendanceRecordsByStudentId(studentId);
    
    // Sort records by date (newest first)
    const sortedRecords = [...records].sort((a, b) => {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
    
    // Log the records for debugging
    console.log(`Found ${sortedRecords.length} attendance records for student ${studentId}`);
    
    return sortedRecords;
  } catch (error) {
    console.error('Error fetching student attendance:', error);
    throw error;
  }
};

// New markAttendance function that matches the signature used in AttendanceManagement component
export const markAttendance = async (data: {
  level_id: string;
  date: string;
  attendance: { student_id: string; present: boolean }[];
}) => {
  try {
    console.log(`Marking attendance for level ${data.level_id} on ${data.date} for ${data.attendance.length} students`);
    
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const records = [];
    const updatedRecords = [];
    
    // Process each student's attendance
    for (const record of data.attendance) {
      const studentId = record.student_id;
      const status = record.present ? 'present' : 'absent';
      
      // Check if record already exists
      const existingRecord = await attendanceModel.getAttendanceRecordByDateAndStudentId(data.date, studentId);
      
      if (existingRecord) {
        // Update existing record
        console.log(`Updating existing attendance record for student ${studentId}`);
        await attendanceModel.updateAttendanceRecord(existingRecord.id, {
          status,
          marked_by: user.uid
        });
        updatedRecords.push(existingRecord.id);
      } else {
        // Create new record
        const student = await studentModel.getStudentById(studentId);
        
        if (student) {
          records.push({
            student_id: studentId,
            date: data.date,
            status,
            notes: '',
            marked_by: user.uid,
            course_id: student.course_id,
            level_id: data.level_id
          });
        }
      }
    }
    
    // Create new records
    const newRecordIds = records.length > 0 ? 
      await attendanceModel.bulkCreateAttendanceRecords(records) : 
      [];
    
    const totalUpdated = updatedRecords.length + newRecordIds.length;
    
    await logActivity('attendance_bulk_created', {
      count: totalUpdated,
      new_count: newRecordIds.length,
      updated_count: updatedRecords.length,
      date: data.date,
      level_id: data.level_id
    });
    
    return [...updatedRecords, ...newRecordIds];
  } catch (error: any) {
    console.error('Error marking attendance:', error);
    throw error;
  }
};

// Original markAttendance function for individual students
export const markStudentAttendance = async (
  studentId: string,
  date: string,
  status: 'present' | 'absent' | 'late' | 'excused',
  notes?: string
) => {
  try {
    console.log(`Marking attendance for student ${studentId} on ${date} as ${status}`);
    
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Check if record already exists
    const existingRecord = await attendanceModel.getAttendanceRecordByDateAndStudentId(date, studentId);
    
    if (existingRecord) {
      console.log(`Updating existing attendance record for student ${studentId}`);
      // Update existing record
      await attendanceModel.updateAttendanceRecord(existingRecord.id, {
        status,
        notes,
        marked_by: user.uid
      });
      
      await logActivity('attendance_updated', {
        studentId,
        date,
        status
      });
      
      toast.success('Attendance updated successfully');
      return existingRecord.id;
    } else {
      console.log(`Creating new attendance record for student ${studentId}`);
      // Create new record
      const student = await studentModel.getStudentById(studentId);
      
      if (!student) {
        throw new Error('Student not found');
      }
      
      const recordId = await attendanceModel.createAttendanceRecord({
        student_id: studentId,
        date,
        status,
        notes: notes || '',
        marked_by: user.uid,
        course_id: student.course_id,
        level_id: student.level_id
      });
      
      console.log(`Created attendance record with ID ${recordId}`);
      
      await logActivity('attendance_created', {
        studentId,
        date,
        status
      });
      
      toast.success('Attendance marked successfully');
      return recordId;
    }
  } catch (error: any) {
    console.error('Error marking attendance:', error);
    toast.error(error.message || 'Failed to mark attendance');
    throw error;
  }
};

export const bulkMarkAttendance = async (
  studentIds: string[],
  date: string,
  status: 'present' | 'absent' | 'late' | 'excused'
) => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const records = [];
    const updatedRecords = [];
    
    for (const studentId of studentIds) {
      // Check if record already exists for this student on this date
      const existingRecord = await attendanceModel.getAttendanceRecordByDateAndStudentId(date, studentId);
      
      if (existingRecord) {
        // Update existing record
        console.log(`Updating existing attendance record for student ${studentId}`);
        await attendanceModel.updateAttendanceRecord(existingRecord.id, {
          status,
          marked_by: user.uid
        });
        updatedRecords.push(existingRecord.id);
      } else {
        // Create new record
        const student = await studentModel.getStudentById(studentId);
        
        if (student) {
          records.push({
            student_id: studentId,
            date,
            status,
            notes: '',
            marked_by: user.uid,
            course_id: student.course_id,
            level_id: student.level_id
          });
        }
      }
    }
    
    // Create new records
    const newRecordIds = records.length > 0 ? 
      await attendanceModel.bulkCreateAttendanceRecords(records) : 
      [];
    
    const totalUpdated = updatedRecords.length + newRecordIds.length;
    
    await logActivity('attendance_bulk_created', {
      count: totalUpdated,
      new_count: newRecordIds.length,
      updated_count: updatedRecords.length,
      date,
      status
    });
    
    toast.success(`Marked attendance for ${totalUpdated} students (${newRecordIds.length} new, ${updatedRecords.length} updated)`);
    return [...updatedRecords, ...newRecordIds];
  } catch (error: any) {
    console.error('Error bulk marking attendance:', error);
    toast.error(error.message || 'Failed to mark attendance');
    throw error;
  }
}; 