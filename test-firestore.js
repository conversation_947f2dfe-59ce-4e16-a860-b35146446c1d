// Script to test Firestore connection
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, getDocs } from 'firebase/firestore';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID,
  measurementId: process.env.VITE_FIREBASE_MEASUREMENT_ID
};

console.log('Using Firebase config:', {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Test function to add and retrieve data
async function testFirestore() {
  try {
    console.log('Testing Firestore connection...');
    
    // Test collection name
    const testCollection = 'firestore_test';
    
    // Add a test document
    const testDoc = {
      message: 'Hello from Firebase!',
      timestamp: new Date(),
      test: true
    };
    
    console.log('Adding test document to Firestore...');
    const docRef = await addDoc(collection(db, testCollection), testDoc);
    console.log('Test document added with ID:', docRef.id);
    
    // Retrieve the test documents
    console.log('Retrieving test documents...');
    const querySnapshot = await getDocs(collection(db, testCollection));
    
    if (querySnapshot.empty) {
      console.log('No documents found in test collection.');
    } else {
      console.log(`Found ${querySnapshot.size} documents in test collection:`);
      querySnapshot.forEach(doc => {
        console.log(`- Document ID: ${doc.id}`);
        console.log('  Data:', doc.data());
      });
    }
    
    console.log('Firestore connection test completed successfully!');
  } catch (error) {
    console.error('Error testing Firestore connection:', error);
  }
}

// Run the test
testFirestore(); 