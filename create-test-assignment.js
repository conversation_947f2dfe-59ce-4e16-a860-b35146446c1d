const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc } = require('firebase/firestore');
require('dotenv').config();

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Create a test assignment
const createTestAssignment = async () => {
  try {
    // Get the level_id from command line args or use a default
    const levelId = process.argv[2] || 'test-level-id';
    
    // Create assignment data
    const assignmentData = {
      title: 'Test Assignment',
      description: 'This is a test assignment created for debugging purposes',
      level_id: levelId,
      due_date: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
      points: 100,
      status: 'active',
      created_by: 'test-user',
      created_at: new Date().toISOString(),
      subject: 'Test Subject'
    };
    
    // Add the document to Firestore
    const docRef = await addDoc(collection(db, 'assignments'), assignmentData);
    console.log('Test assignment created with ID:', docRef.id);
    console.log('Assignment data:', assignmentData);
    
  } catch (error) {
    console.error('Error creating test assignment:', error);
  }
};

createTestAssignment(); 